/**
 * Example showing how to integrate PangeaFlow diagram generation 
 * with a frontend application
 */

import { generatePangeaFlowDiagram, WorkflowOrchestrator } from '../pangeaflow/pangeaflow';
i
import * as fs from 'fs';
import * as path from 'path';

/**
 * Service class for generating workflow diagrams for frontend consumption
 */
export class WorkflowDiagramService {
  private readonly outputDir: string;

  constructor(outputDir: string = './public/diagrams') {
    this.outputDir = outputDir;
    this.ensureOutputDirectory();
  }

  /**
   * Generate a workflow diagram and return the file path for frontend use
   */
  async generateWorkflowDiagram(
    workflow: WorkflowOrchestrator,
    workflowId: string,
    options?: {
      theme?: 'light' | 'dark' | 'minimal';
      size?: 'small' | 'medium' | 'large';
      showDetails?: boolean;
    }
  ): Promise<{
    filePath: string;
    publicUrl: string;
    metadata: {
      width: number;
      height: number;
      theme: string;
      generatedAt: string;
    };
  }> {
    const theme = options?.theme || 'light';
    const size = options?.size || 'medium';
    const showDetails = options?.showDetails ?? true;

    // Generate filename
    const filename = `workflow-${workflowId}-${theme}-${size}.png`;
    const filePath = path.join(this.outputDir, filename);
    const publicUrl = `/diagrams/${filename}`;

    // Get theme-specific styling
    const diagramOptions = this.getThemeOptions(theme, size, showDetails);

    // Generate the diagram
    await generatePangeaFlowDiagram(workflow, filePath, diagramOptions);

    return {
      filePath,
      publicUrl,
      metadata: {
        width: diagramOptions.width!,
        height: diagramOptions.height!,
        theme,
        generatedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Generate multiple diagram variants for a workflow
   */
  async generateWorkflowVariants(
    workflow: WorkflowOrchestrator,
    workflowId: string
  ): Promise<{
    light: string;
    dark: string;
    minimal: string;
    thumbnail: string;
  }> {
    const variants = await Promise.all([
      this.generateWorkflowDiagram(workflow, workflowId, { theme: 'light', size: 'large' }),
      this.generateWorkflowDiagram(workflow, workflowId, { theme: 'dark', size: 'large' }),
      this.generateWorkflowDiagram(workflow, workflowId, { theme: 'minimal', size: 'medium' }),
      this.generateWorkflowDiagram(workflow, workflowId, { theme: 'light', size: 'small', showDetails: false }),
    ]);

    return {
      light: variants[0].publicUrl,
      dark: variants[1].publicUrl,
      minimal: variants[2].publicUrl,
      thumbnail: variants[3].publicUrl,
    };
  }

  /**
   * Get theme-specific styling options
   */
  private getThemeOptions(theme: string, size: string, showDetails: boolean) {
    const sizeConfig = {
      small: { width: 800, height: 500, fontSize: 10 },
      medium: { width: 1200, height: 800, fontSize: 12 },
      large: { width: 1600, height: 1200, fontSize: 14 },
    };

    const themeConfig = {
      light: {
        backgroundColor: '#ffffff',
        componentColor: '#f1f5f9',
        actionColor: '#f8fafc',
        edgeColor: '#64748b',
        textColor: '#334155',
      },
      dark: {
        backgroundColor: '#1e293b',
        componentColor: '#334155',
        actionColor: '#475569',
        edgeColor: '#60a5fa',
        textColor: '#f1f5f9',
      },
      minimal: {
        backgroundColor: '#ffffff',
        componentColor: '#f9fafb',
        actionColor: '#f3f4f6',
        edgeColor: '#9ca3af',
        textColor: '#374151',
      },
    };

    const sizeSettings = sizeConfig[size as keyof typeof sizeConfig];
    const themeSettings = themeConfig[theme as keyof typeof themeConfig];

    return {
      ...sizeSettings,
      ...themeSettings,
      showComponentTypes: showDetails,
      showActionLabels: showDetails,
      padding: size === 'small' ? 30 : size === 'medium' ? 50 : 70,
    };
  }

  /**
   * Ensure output directory exists
   */
  private ensureOutputDirectory(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * Clean up old diagram files
   */
  async cleanupOldDiagrams(maxAgeHours: number = 24): Promise<void> {
    const files = fs.readdirSync(this.outputDir);
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

    for (const file of files) {
      if (file.endsWith('.png')) {
        const filePath = path.join(this.outputDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(filePath);
          console.log(`Cleaned up old diagram: ${file}`);
        }
      }
    }
  }
}

/**
 * Express.js route handler example
 */
export function createDiagramRouteHandler(diagramService: WorkflowDiagramService) {
  return async (req: any, res: any) => {
    try {
      const { workflowId, theme = 'light', size = 'medium' } = req.query;
      
      if (!workflowId) {
        return res.status(400).json({ error: 'workflowId is required' });
      }

      // Get workflow from your application logic
      const workflow = await getWorkflowById(workflowId); // Implement this function
      
      if (!workflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      const result = await diagramService.generateWorkflowDiagram(
        workflow,
        workflowId,
        { theme, size }
      );

      res.json({
        success: true,
        diagram: result,
      });

    } catch (error) {
      console.error('Error generating diagram:', error);
      res.status(500).json({ 
        error: 'Failed to generate workflow diagram',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  };
}

/**
 * Placeholder function - implement based on your application
 */
async function getWorkflowById(workflowId: string): Promise<WorkflowOrchestrator | null> {
  // Implement your workflow retrieval logic here
  // This could fetch from database, cache, etc.
  throw new Error('getWorkflowById not implemented - replace with your logic');
}

/**
 * React component example (TypeScript)
 */
export const WorkflowDiagramComponent = `
import React, { useState, useEffect } from 'react';

interface WorkflowDiagramProps {
  workflowId: string;
  theme?: 'light' | 'dark' | 'minimal';
  size?: 'small' | 'medium' | 'large';
}

export const WorkflowDiagram: React.FC<WorkflowDiagramProps> = ({
  workflowId,
  theme = 'light',
  size = 'medium'
}) => {
  const [diagramUrl, setDiagramUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const generateDiagram = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          \`/api/workflow-diagram?workflowId=\${workflowId}&theme=\${theme}&size=\${size}\`
        );
        
        if (!response.ok) {
          throw new Error('Failed to generate diagram');
        }
        
        const data = await response.json();
        setDiagramUrl(data.diagram.publicUrl);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    generateDiagram();
  }, [workflowId, theme, size]);

  if (loading) {
    return <div className="animate-pulse bg-gray-200 rounded h-64 w-full" />;
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-4">
        <p className="text-red-600">Error: {error}</p>
      </div>
    );
  }

  return (
    <div className="workflow-diagram">
      {diagramUrl && (
        <img
          src={diagramUrl}
          alt={\`Workflow diagram for \${workflowId}\`}
          className="max-w-full h-auto rounded shadow-lg"
        />
      )}
    </div>
  );
};
`;
