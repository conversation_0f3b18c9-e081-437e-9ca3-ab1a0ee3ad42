// src/Agents/Code2Documentation/pangeaflow/agents/FetchRepoAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { emitGraphStatus, emitProgress, emitError } from '../pangeaflow/utils/events';
import { fetch_selected_github_files } from '../utils/crawl_github_files';
import { fetch_selected_local_files } from '../utils/crawl_local_files';

export class FetchRepoAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('fetch-repo', eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("FetchRepo", 0, "Starting repository fetch");
    
    try {
      // Extract parameters from context
      const { 
        repo_url, 
        local_dir, 
        github_token, 
        selected_files, 
        user_id, 
        project_name: initialProjectName 
      } = context.sharedState;

      // Determine project name if not provided
      let project_name = initialProjectName;
      if (!project_name && repo_url) {
        const parts = (repo_url as string).split("/");
        const lastPart = parts[parts.length - 1];
        project_name = lastPart.replace(".git", "");
      } else if (!project_name && local_dir) {
        project_name = (local_dir as string).split(/[\\/\\]/).pop();
      } else if (!project_name) {
        project_name = "Local Project";
      }

      console.log('Project Name: ', project_name);
      
      // Generate tutorial ID
      const tutorialId = `${(project_name as string).replace(/[^a-zA-Z0-9-_]/g, '-')}-${Date.now()}`;
      console.log('Tutorial Id: ', tutorialId);
      
      emitGraphStatus("FetchRepo", 10, `Preparing to fetch files for ${project_name}`);

      // Prepare arguments for file fetching
      const fetchArgs = {
        repo_url,
        local_dir,
        token: github_token,
        selected_files: selected_files || [],
        use_relative_paths: true
      };

      emitGraphStatus("FetchRepo", 20, "Starting file retrieval");

      let files: any;
      const selectedFilesArray = fetchArgs.selected_files as string[] | [string, string][];

      // Check if selected_files is already in [path, content] format
      if (selectedFilesArray.length > 0 && Array.isArray(selectedFilesArray[0]) && selectedFilesArray[0].length === 2) {
        // Files are already provided as [path, content] tuples
        console.log("Using provided file contents directly");
        emitGraphStatus("FetchRepo", 30, `Using ${selectedFilesArray.length} provided files`);

        const filesMap: Record<string, string> = {};
        (selectedFilesArray as [string, string][]).forEach(([path, content]) => {
          filesMap[path] = content;
        });

        files = { files: filesMap };
        emitGraphStatus("FetchRepo", 60, "File contents loaded from provided data");

      } else if (fetchArgs.repo_url) {
        console.log("Fetching selected files from repository..", fetchArgs.repo_url);
        emitGraphStatus("FetchRepo", 30, `Fetching ${selectedFilesArray.length} selected files from GitHub repository`);

        files = await fetch_selected_github_files(
          fetchArgs.repo_url as string,
          selectedFilesArray as string[],
          {
            githubToken: fetchArgs.token as string,
            useRelativePaths: fetchArgs.use_relative_paths,
          }
        );

        emitGraphStatus("FetchRepo", 60, "GitHub repository files retrieved");

      } else if (fetchArgs.local_dir) {
        emitGraphStatus("FetchRepo", 30, `Fetching ${selectedFilesArray.length} selected files from local directory`);

        files = await fetch_selected_local_files(
          fetchArgs.local_dir as string,
          selectedFilesArray as string[],
          {
            useRelativePaths: fetchArgs.use_relative_paths,
          }
        );

        emitGraphStatus("FetchRepo", 60, "Local directory files retrieved");
      } else {
        emitGraphStatus("FetchRepo", 30, "Error: No source specified");
        throw new Error("Either repo_url, local_dir, or file contents in selected_files must be provided");
      }

      const files_list = Object.entries(files.files || {});
      if (files_list.length === 0) {
        emitGraphStatus("FetchRepo", 70, "Error: No files found");
        throw new Error("Failed to fetch files");
      }

      console.log(`Fetched ${files_list.length} files.`);
      emitGraphStatus("FetchRepo", 80, `Retrieved ${files_list.length} files`);

      // Emit progress event
      emitProgress("Repository Fetching", 20, `Fetched ${files_list.length} files`);

      // Emit final graph status for this agent
      emitGraphStatus("FetchRepo", 100, `Completed fetching ${files_list.length} files`);

      // Return success result with next action
      return {
        success: true,
        output: {
          files: files_list,
          project_name,
          user_id,
          tutorial_id: tutorialId
        },
        events: [],
        nextActions: ['identify-abstractions'],
        sharedStateUpdates: {
          ...context.sharedState,
          files: files_list,
          project_name,
          tutorial_id: tutorialId
        }
      };
    } catch (error) {
      // Emit error event for UI
      emitError("FetchRepo", (error as Error).message, error as Error);
      emitGraphStatus("FetchRepo", 100, `Error: ${(error as Error).message}`);

      // Log error details
      console.error("FetchRepo agent error:", error);

      // Return error result
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        sharedStateUpdates: context.sharedState
      };
    }
  }
}
