import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, BookOpen, ArrowRight, Zap, Target, Users, Code, GraduationCap } from "lucide-react";

interface CreateOptionsCardProps {
  className?: string;
}

const CreateOptionsCard: React.FC<CreateOptionsCardProps> = ({ className }) => {
  return (
    <div className={`grid md:grid-cols-2 gap-8 ${className}`}>
      {/* Documentation Option */}
      <Card className="relative overflow-hidden border-2 hover:border-blue-300 transition-all duration-200 hover:shadow-lg">
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            Technical Docs
          </Badge>
        </div>
        
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Code Documentation</CardTitle>
          </div>
          <CardDescription className="text-base">
            Generate comprehensive technical documentation from your codebase with API references, 
            architecture overviews, and detailed code explanations.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <Code className="h-4 w-4 mr-2 text-blue-600" />
              <span>API documentation and code references</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <FileText className="h-4 w-4 mr-2 text-blue-600" />
              <span>Architecture diagrams and explanations</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Target className="h-4 w-4 mr-2 text-blue-600" />
              <span>Perfect for developers and maintainers</span>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-700">Best for:</span>
              <div className="flex space-x-2">
                <Badge variant="outline" className="text-xs">API Docs</Badge>
                <Badge variant="outline" className="text-xs">Reference</Badge>
              </div>
            </div>
            
            <Link to="/dashboard" className="block">
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                Create Documentation
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Tutorial Option */}
      <Card className="relative overflow-hidden border-2 hover:border-green-300 transition-all duration-200 hover:shadow-lg">
        <div className="absolute top-4 right-4">
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            Interactive
          </Badge>
        </div>
        
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3 mb-2">
            <div className="p-2 bg-green-100 rounded-lg">
              <BookOpen className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">Interactive Tutorial</CardTitle>
          </div>
          <CardDescription className="text-base">
            Transform your code into engaging learning experiences with hands-on exercises, 
            quizzes, and step-by-step guidance for different skill levels.
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center text-sm text-gray-600">
              <Zap className="h-4 w-4 mr-2 text-green-600" />
              <span>Interactive exercises and coding challenges</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Users className="h-4 w-4 mr-2 text-green-600" />
              <span>Adaptive content for all skill levels</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <GraduationCap className="h-4 w-4 mr-2 text-green-600" />
              <span>Perfect for learning and teaching</span>
            </div>
          </div>

          <div className="pt-4 border-t border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-700">Best for:</span>
              <div className="flex space-x-2">
                <Badge variant="outline" className="text-xs">Learning</Badge>
                <Badge variant="outline" className="text-xs">Teaching</Badge>
              </div>
            </div>
            
            <Link to="/dashboard/create-tutor" className="block">
              <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                Create Tutorial
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CreateOptionsCard;
