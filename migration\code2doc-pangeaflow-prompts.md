# Code2Documentation PangeaFlow Migration Prompts

This document contains prompts for automatically converting the Code2Documentation system from PocketFlow to PangeaFlow architecture.

## 1. Agent Naming Convention

### Prompt: Define Agent Naming Convention

Convert each PocketFlow Node to a PangeaFlow Agent using the following naming convention:

1. Take the original Node class name (e.g., `FetchRepo`)
2. Add "Agent" suffix (e.g., `FetchRepoAgent`)
3. Maintain the same functionality and purpose
4. Use PangeaFlow's execution context and result patterns

**Original Node Names:**
- FetchRepo
- IdentifyAbstractions
- AnalyzeRelationships
- OrderChapters
- WriteChapters (BatchNode)
- CombineTutorial

**New Agent Names:**
- FetchRepoAgent
- IdentifyAbstractionsAgent
- AnalyzeRelationshipsAgent
- OrderChaptersAgent
- WriteChaptersAgent
- CombineTutorialAgent

## 2. FetchRepoAgent Implementation

### Prompt: Convert FetchRepo Node to FetchRepoAgent

Convert the PocketFlow `FetchRepo` node to a PangeaFlow `FetchRepoAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { emitGraphStatus, emitProgress } from "../utils/events";

export class FetchRepoAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('fetch-repo', eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("FetchRepo", 0, "Starting repository fetch");
    
    try {
      // Extract parameters from context
      const { repo_url, project_name, user_id, tutorial_id } = context.metadata;
      
      // Implement the same logic as the original FetchRepo node's process method
      // ...
      
      // Return success result with next action
      return {
        success: true,
        data: {
          files: fetchedFiles,
          project_name,
          user_id,
          tutorial_id
        },
        events: [],
        nextActions: ['identify-abstractions'],
        metadata: {
          ...context.metadata,
          files: fetchedFiles
        }
      };
    } catch (error) {
      // Return error result
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

Ensure all functionality from the original node is preserved, including error handling and event emission.

## 3. IdentifyAbstractionsAgent Implementation

### Prompt: Convert IdentifyAbstractions Node to IdentifyAbstractionsAgent

Convert the PocketFlow `IdentifyAbstractions` node to a PangeaFlow `IdentifyAbstractionsAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../utils/buildPrompt";
import { IDENTIFY_ABSTRACTIONS_PROMPT } from "../prompts/identifyAbstractions";
import { emitGraphStatus, emitProgress } from "../utils/events";
import yaml from 'js-yaml';

export class IdentifyAbstractionsAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('identify-abstractions', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("IdentifyAbstractions", 0, "Starting abstraction identification");
    
    try {
      // Extract parameters from context
      const { files, project_name, language, use_cache, user_id, tutorial_id } = context.metadata;
      const max_abstraction_num = context.metadata.max_abstraction_num || 10;
      
      // Implement the same preparation logic as in the original prep method
      // ...
      
      // Build prompt and call LLM
      // ...
      
      // Parse results and return success
      return {
        success: true,
        data: {
          abstractions: parsedAbstractions
        },
        events: [],
        nextActions: ['analyze-relationships'],
        metadata: {
          ...context.metadata,
          abstractions: parsedAbstractions
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = context.metadata.retryCount || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['identify-abstractions'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

Ensure all functionality from the original node is preserved, including error handling, retry logic, and event emission.

## 4. AnalyzeRelationshipsAgent Implementation

### Prompt: Convert AnalyzeRelationships Node to AnalyzeRelationshipsAgent

Convert the PocketFlow `AnalyzeRelationships` node to a PangeaFlow `AnalyzeRelationshipsAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../utils/buildPrompt";
import { ANALYZE_RELATIONSHIPS_PROMPT } from "../prompts/analyzeRelationships";
import { emitGraphStatus, emitProgress } from "../utils/events";
import yaml from 'js-yaml';

export class AnalyzeRelationshipsAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('analyze-relationships', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("AnalyzeRelationships", 0, "Starting relationship analysis");
    
    try {
      // Extract parameters from context
      const { abstractions, files, project_name, language, use_cache, user_id, tutorial_id } = context.metadata;
      
      // Implement the same preparation logic as in the original prep method
      // ...
      
      // Build prompt and call LLM
      // ...
      
      // Parse results and return success
      return {
        success: true,
        data: {
          relationships: parsedRelationships,
          summary: parsedSummary
        },
        events: [],
        nextActions: ['order-chapters'],
        metadata: {
          ...context.metadata,
          relationships: parsedRelationships,
          summary: parsedSummary
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = context.metadata.retryCount || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['analyze-relationships'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## 5. OrderChaptersAgent Implementation

### Prompt: Convert OrderChapters Node to OrderChaptersAgent

Convert the PocketFlow `OrderChapters` node to a PangeaFlow `OrderChaptersAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../utils/buildPrompt";
import { ORDER_CHAPTERS_PROMPT } from "../prompts/orderChapters";
import { emitGraphStatus, emitProgress } from "../utils/events";
import yaml from 'js-yaml';

export class OrderChaptersAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('order-chapters', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("OrderChapters", 0, "Starting chapter ordering");
    
    try {
      // Extract parameters from context
      const { abstractions, relationships, summary, project_name, language, use_cache, user_id, tutorial_id } = context.metadata;
      
      // Implement the same preparation logic as in the original prep method
      // ...
      
      // Build prompt and call LLM
      // ...
      
      // Parse results and return success
      return {
        success: true,
        data: {
          orderedAbstractions: parsedOrderedAbstractions
        },
        events: [],
        nextActions: ['write-chapters'],
        metadata: {
          ...context.metadata,
          orderedAbstractions: parsedOrderedAbstractions
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = context.metadata.retryCount || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['order-chapters'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## 6. WriteChaptersAgent Implementation

### Prompt: Convert WriteChapters BatchNode to WriteChaptersAgent

Convert the PocketFlow `WriteChapters` BatchNode to a PangeaFlow `WriteChaptersAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../utils/buildPrompt";
import { WRITE_CHAPTER_PROMPT } from "../prompts/writeChapters";
import { emitGraphStatus, emitProgress } from "../utils/events";

export class WriteChaptersAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;
  private currentChapterIndex: number = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('write-chapters', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { orderedAbstractions } = context.metadata;
    
    // Initialize chapters array if not present
    if (!context.metadata.chapterContents) {
      context.metadata.chapterContents = [];
      this.currentChapterIndex = 0;
    }
    
    // Check if all chapters are processed
    if (this.currentChapterIndex >= orderedAbstractions.length) {
      return {
        success: true,
        data: {
          chapterContents: context.metadata.chapterContents
        },
        events: [],
        nextActions: ['combine-tutorial'],
        metadata: context.metadata
      };
    }
    
    // Get current chapter to process
    const chapter = orderedAbstractions[this.currentChapterIndex];
    const chapterNum = this.currentChapterIndex + 1;
    
    emitGraphStatus("WriteChapters", 50 + (this.currentChapterIndex / orderedAbstractions.length) * 40, 
      `Writing chapter ${chapterNum}/${orderedAbstractions.length}: ${chapter.name}`);
    
    try {
      // Implement chapter writing logic
      // ...
      
      // Add chapter to results
      context.metadata.chapterContents.push({
        name: chapter.name,
        content: chapterContent
      });
      
      // Increment chapter index
      this.currentChapterIndex++;
      
      // Return result with next action (loop back to write next chapter)
      return {
        success: true,
        data: {
          currentChapter: {
            name: chapter.name,
            content: chapterContent
          }
        },
        events: [],
        nextActions: ['write-chapters'],
        metadata: context.metadata
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = context.metadata.retryCount || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['write-chapters'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## 7. CombineTutorialAgent Implementation

### Prompt: Convert CombineTutorial Node to CombineTutorialAgent

Convert the PocketFlow `CombineTutorial` node to a PangeaFlow `CombineTutorialAgent` using this template:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { emitGraphStatus, emitProgress } from "../utils/events";

export class CombineTutorialAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('combine-tutorial', eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("CombineTutorial", 90, "Combining tutorial chapters");
    
    try {
      // Extract parameters from context
      const { chapterContents, summary, project_name } = context.metadata;
      
      // Implement the same logic as the original process method
      // ...
      
      // Return success result with final tutorial
      return {
        success: true,
        data: {
          tutorial: combinedTutorial,
          project_name,
          summary
        },
        events: [],
        nextActions: ['complete'],
        metadata: {
          ...context.metadata,
          tutorial: combinedTutorial
        }
      };
    } catch (error) {
      // Return error result
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## 8. Workflow Builder Implementation

### Prompt: Create PangeaFlow Workflow for Code2Documentation

Create a workflow builder implementation for Code2Documentation using PangeaFlow:

```typescript
import { WorkflowBuilder } from '@/pangeaflow';
import { FetchRepoAgent } from '../agents/FetchRepoAgent';
import { IdentifyAbstractionsAgent } from '../agents/IdentifyAbstractionsAgent';
import { AnalyzeRelationshipsAgent } from '../agents/AnalyzeRelationshipsAgent';
import { OrderChaptersAgent } from '../agents/OrderChaptersAgent';
import { WriteChaptersAgent } from '../agents/WriteChaptersAgent';
import { CombineTutorialAgent } from '../agents/CombineTutorialAgent';
import { ErrorHandlerAgent } from '../agents/ErrorHandlerAgent';

export function createCode2DocumentationWorkflow(eventBus: any, telemetry: any) {
  // Create the workflow
  const builder = WorkflowBuilder.create();
  
  // Add agents
  builder
    .addAgent('fetch-repo', new FetchRepoAgent(eventBus, telemetry))
    .addAgent('identify-abstractions', new IdentifyAbstractionsAgent(eventBus, telemetry, 5, 20))
    .addAgent('analyze-relationships', new AnalyzeRelationshipsAgent(eventBus, telemetry, 5, 20))
    .addAgent('order-chapters', new OrderChaptersAgent(eventBus, telemetry, 5, 20))
    .addAgent('write-chapters', new WriteChaptersAgent(eventBus, telemetry, 5, 20))
    .addAgent('combine-tutorial', new CombineTutorialAgent(eventBus, telemetry))
    .addAgent('error-handler', new ErrorHandlerAgent(eventBus, telemetry));
  
  // Define workflow routes
  builder
    .route('start', 'fetch-repo')
    .route('identify-abstractions', 'identify-abstractions')
    .route('analyze-relationships', 'analyze-relationships')
    .route('order-chapters', 'order-chapters')
    .route('write-chapters', 'write-chapters')
    .route('combine-tutorial', 'combine-tutorial')
    .route('complete', 'complete')
    .route('error', 'error-handler');
  
  return builder.build();
}

export async function executeCode2DocumentationWorkflow(params: any) {
  const eventBus = new EventBus();
  const telemetry = new TelemetryCollector();
  
  const workflow = createCode2DocumentationWorkflow(eventBus, telemetry);
  
  // Initialize context with input parameters
  const context = {
    metadata: {
      ...params,
      retryCount: 0
    }
  };
  
  // Execute workflow
  return workflow.execute('start', context);
}
```

## 9. Error Handler Implementation

### Prompt: Create Error Handler Agent

Create an error handler agent for the Code2Documentation workflow:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { emitGraphStatus, emitError } from "../utils/events";

export class ErrorHandlerAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('error-handler', eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const error = context.error || new Error("Unknown error occurred");
    
    emitError("Code2Documentation", error.message);
    emitGraphStatus("ErrorHandler", 100, `Error: ${error.message}`);
    
    // Log error details
    console.error("Code2Documentation workflow error:", error);
    
    // Return error result
    return {
      success: false,
      error,
      events: [],
      nextActions: [],
      metadata: context.metadata
    };
  }
}
```

## 10. Integration with Create.tsx

### Prompt: Update Create.tsx to Support Both Implementations

Update the Create.tsx component to support both PocketFlow and PangeaFlow implementations:

```typescript
import React, { useState } from 'react';
import { create_tutorial_flow } from '../Agents/Code2Tutor/flow/flow';
import { executeCode2DocumentationWorkflow } from '../Agents/Code2Documentation/flow/pangeaFlow';
import { create_tutorial_flow as create_pocketflow_tutorial } from '../Agents/Code2Documentation/flow/flow';

// Add a workflow engine selection option to the form
const [formState, setFormState] = useState({
  // ... existing form state
  workflowEngine: 'pocketflow', // 'pocketflow' or 'pangeaflow'
});

// Update the handleStartGeneration function
const handleStartGeneration = async (e: React.FormEvent) => {
  e.preventDefault();
  
  // ... existing code
  
  try {
    // Create shared data object
    const shared = {
      user_id: session?.user?.id,
      repo_url: repoUrl,
      github_token: options.githubToken || "",
      selected_files: selectedFiles,
      language: options.contentLanguage || "english",
      use_cache: options.advancedOptions?.cacheDuration !== 0,
      max_abstraction_num: options.maxAbstractions || 10,
      final_output_dir: "./output",
      // ... other parameters
    };
    
    // Execute workflow based on selected engine
    if (formState.workflowEngine === 'pangeaflow') {
      // Use PangeaFlow implementation
      const result = await executeCode2DocumentationWorkflow(shared);
      
      // Handle result
      if (result.success) {
        // Process successful result
        setTutorialData(result.data.tutorial);
      } else {
        // Handle error
        console.error("Error generating tutorial:", result.error);
        setError(result.error.message);
      }
    } else {
      // Use original PocketFlow implementation
      const flow = create_pocketflow_tutorial();
      await flow.run(shared);
      
      // Original result handling
      // ...
    }
  } catch (error) {
    console.error("Error running tutorial flow:", error);
    setError(error.message);
  }
};

// Add UI for selecting workflow engine
<div className="mb-6">
  <label className="block text-sm font-medium text-gray-700 mb-1">
    Workflow Engine
  </label>
  <div className="flex items-center space-x-4">
    <label className="inline-flex items-center">
      <input
        type="radio"
        className="form-radio"
        name="workflowEngine"
        value="pocketflow"
        checked={formState.workflowEngine === 'pocketflow'}
        onChange={() => setFormState({...formState, workflowEngine: 'pocketflow'})}
      />
      <span className="ml-2">PocketFlow (Original)</span>
    </label>
    <label className="inline-flex items-center">
      <input
        type="radio"
        className="form-radio"
        name="workflowEngine"
        value="pangeaflow"
        checked={formState.workflowEngine === 'pangeaflow'}
        onChange={() => setFormState({...formState, workflowEngine: 'pangeaflow'})}
      />
      <span className="ml-2">PangeaFlow (New)</span>
    </label>
  </div>
</div>
```

## 11. Shared Store Type Definition

### Prompt: Create Shared Type Definitions

Create shared type definitions for the PangeaFlow implementation:

```typescript
// src/Agents/Code2Documentation/types.ts

export interface SharedStore {
  // User and session information
  user_id?: string;
  tutorial_id?: string;
  
  // Repository information
  repo_url: string;
  project_name?: string;
  github_token?: string;
  
  // File selection
  selected_files: string[];
  
  // Configuration options
  language: string;
  use_cache: boolean;
  max_abstraction_num: number;
  final_output_dir: string;
  
  // Processing state
  files?: any[];
  abstractions?: any[];
  relationships?: any[];
  summary?: string;
  orderedAbstractions?: any[];
  chapterContents?: any[];
  tutorial?: string;
  
  // Error handling
  retryCount?: number;
}

export interface ChapterContent {
  name: string;
  content: string;
}

export interface Abstraction {
  name: string;
  description: string;
  files: string[];
}

export interface Relationship {
  source: string;
  target: string;
  type: string;
  description: string;
}
```

## 12. Event System Implementation

### Prompt: Create Event System for PangeaFlow

Create an event system for the PangeaFlow implementation:

```typescript
// src/Agents/Code2Documentation/utils/events.ts

import { EventEmitter } from 'events';

// Create a global event emitter
export const eventEmitter = new EventEmitter();

// Event types
export enum EventType {
  PROGRESS = 'progress',
  STATUS = 'status',
  ERROR = 'error',
  COMPLETE = 'complete'
}

// Event interfaces
export interface ProgressEvent {
  component: string;
  progress: number;
  message: string;
}

export interface StatusEvent {
  component: string;
  status: string;
  message: string;
}

export interface ErrorEvent {
  component: string;
  message: string;
  error: Error;
}

export interface CompleteEvent {
  component: string;
  result: any;
}

// Event emission helpers
export function emitProgress(component: string, progress: number, message: string): void {
  eventEmitter.emit(EventType.PROGRESS, { component, progress, message });
}

export function emitGraphStatus(component: string, progress: number, message: string): void {
  emitProgress(component, progress, message);
  eventEmitter.emit(EventType.STATUS, { component, status: 'running', message });
}

export function emitError(component: string, message: string, error?: Error): void {
  eventEmitter.emit(EventType.ERROR, { 
    component, 
    message, 
    error: error || new Error(message) 
  });
}

export function emitComplete(component: string, result: any): void {
  eventEmitter.emit(EventType.COMPLETE, { component, result });
}

// Event subscription helpers
export function onProgress(callback: (event: ProgressEvent) => void): void {
  eventEmitter.on(EventType.PROGRESS, callback);
}

export function onStatus(callback: (event: StatusEvent) => void): void {
  eventEmitter.on(EventType.STATUS, callback);
}

export function onError(callback: (event: ErrorEvent) => void): void {
  eventEmitter.on(EventType.ERROR, callback);
}

export function onComplete(callback: (event: CompleteEvent) => void): void {
  eventEmitter.on(EventType.COMPLETE, callback);
}

// Cleanup helper
export function removeAllListeners(): void {
  eventEmitter.removeAllListeners();
}
```

## 13. Telemetry Implementation

### Prompt: Create Telemetry Collector for PangeaFlow

Create a telemetry collector for the PangeaFlow implementation:

```typescript
// src/Agents/Code2Documentation/utils/telemetry.ts

export class TelemetryCollector {
  private metrics: Map<string, any[]> = new Map();
  private timers: Map<string, number> = new Map();
  
  // Start timing an operation
  startTimer(operation: string): void {
    this.timers.set(operation, Date.now());
  }
  
  // End timing an operation and record the duration
  endTimer(operation: string): number {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      console.warn(`No timer found for operation: ${operation}`);
      return 0;
    }
    
    const duration = Date.now() - startTime;
    this.recordMetric(operation, { duration });
    this.timers.delete(operation);
    
    return duration;
  }
  
  // Record a metric
  recordMetric(name: string, value: any): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metrics = this.metrics.get(name)!;
    metrics.push({
      ...value,
      timestamp: Date.now()
    });
  }
  
  // Get all metrics
  getMetrics(): Record<string, any[]> {
    const result: Record<string, any[]> = {};
    
    for (const [key, value] of this.metrics.entries()) {
      result[key] = value;
    }
    
    return result;
  }
  
  // Get metrics for a specific operation
  getMetricsForOperation(operation: string): any[] {
    return this.metrics.get(operation) || [];
  }
  
  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.timers.clear();
  }
}
```

## 14. Testing Strategy

### Prompt: Create Testing Strategy for PangeaFlow Implementation

Create a testing strategy for the PangeaFlow implementation:

```typescript
// src/Agents/Code2Documentation/tests/pangeaFlow.test.ts

import { createCode2DocumentationWorkflow, executeCode2DocumentationWorkflow } from '../flow/pangeaFlow';
import { SharedStore } from '../types';
import { EventType, eventEmitter } from '../utils/events';

describe('Code2Documentation PangeaFlow Tests', () => {
  // Mock event bus and telemetry
  const mockEventBus = {
    emit: jest.fn(),
    on: jest.fn()
  };
  
  const mockTelemetry = {
    startTimer: jest.fn(),
    endTimer: jest.fn(),
    recordMetric: jest.fn()
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('Workflow creation', () => {
    const workflow = createCode2DocumentationWorkflow(mockEventBus, mockTelemetry);
    expect(workflow).toBeDefined();
  });
  
  test('End-to-end workflow execution with mock data', async () => {
    // Create test data
    const testParams: SharedStore = {
      user_id: 'test-user',
      repo_url: 'https://github.com/test/repo',
      project_name: 'Test Project',
      selected_files: ['file1.js', 'file2.js'],
      language: 'english',
      use_cache: true,
      max_abstraction_num: 5,
      final_output_dir: './test-output'
    };
    
    // Mock agent implementations
    // ...
    
    // Execute workflow
    const result = await executeCode2DocumentationWorkflow(testParams);
    
    // Verify result
    expect(result.success).toBe(true);
    expect(result.data.tutorial).toBeDefined();
  });
  
  test('Error handling', async () => {
    // Create test data with invalid parameters
    const testParams: SharedStore = {
      user_id: 'test-user',
      repo_url: 'invalid-url',
      selected_files: [],
      language: 'english',
      use_cache: true,
      max_abstraction_num: 5,
      final_output_dir: './test-output'
    };
    
    // Mock error event listener
    const errorHandler = jest.fn();
    eventEmitter.on(EventType.ERROR, errorHandler);
    
    // Execute workflow
    const result = await executeCode2DocumentationWorkflow(testParams);
    
    // Verify error handling
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(errorHandler).toHaveBeenCalled();
  });
});
```

## 15. Migration Guide

### Prompt: Create Migration Guide for PangeaFlow Implementation

Create a migration guide for the PangeaFlow implementation:

```markdown
# Code2Documentation Migration Guide

This guide explains how to migrate from the PocketFlow implementation to the PangeaFlow implementation of Code2Documentation.

## Overview

The Code2Documentation system has been migrated from PocketFlow to PangeaFlow to provide better:

- Error handling
- Progress reporting
- Telemetry
- Integration with other systems

## Key Differences

### Architecture

- **PocketFlow**: Linear flow with nodes connected in sequence
- **PangeaFlow**: Event-driven workflow with agents and routes

### State Management

- **PocketFlow**: Shared store passed between nodes
- **PangeaFlow**: Execution context with metadata

### Error Handling

- **PocketFlow**: Basic error handling with retries
- **PangeaFlow**: Comprehensive error handling with dedicated error agent

### Progress Reporting

- **PocketFlow**: Basic progress events
- **PangeaFlow**: Detailed progress and status events

## Migration Steps

1. **Update Imports**:
   ```typescript
   // Old
   import { create_tutorial_flow } from '../Agents/Code2Documentation/flow/flow';
   
   // New
   import { executeCode2DocumentationWorkflow } from '../Agents/Code2Documentation/flow/pangeaFlow';
   ```

2. **Update Function Calls**:
   ```typescript
   // Old
   const flow = create_tutorial_flow();
   await flow.run(shared);
   
   // New
   const result = await executeCode2DocumentationWorkflow(shared);
   ```

3. **Handle Results**:
   ```typescript
   // New
   if (result.success) {
     // Process successful result
     const tutorial = result.data.tutorial;
   } else {
     // Handle error
     console.error("Error:", result.error);
   }
   ```

4. **Subscribe to Events**:
   ```typescript
   import { onProgress, onStatus, onError, onComplete } from '../Agents/Code2Documentation/utils/events';
   
   onProgress((event) => {
     console.log(`Progress: ${event.component} - ${event.progress}% - ${event.message}`);
   });
   
   onError((event) => {
     console.error(`Error: ${event.component} - ${event.message}`);
   });
   ```

## Testing

Run the test suite to verify the migration:

```bash
npm test -- --testPathPattern=Code2Documentation
```

## Rollback Plan

If issues are encountered, you can easily switch back to the PocketFlow implementation:

```typescript
import { create_tutorial_flow } from '../Agents/Code2Documentation/flow/flow';

const flow = create_tutorial_flow();
await flow.run(shared);
```
```

## 16. Implementation Guidelines

### Prompt: Create Implementation Guidelines for PangeaFlow Migration

Create implementation guidelines for the PangeaFlow migration:

```markdown
# Implementation Guidelines

## Prompt Reuse Requirements

1. **All prompts from `src/Agents/Code2Documentation/prompts` directory MUST be reused exactly as-is in the PangeaFlow implementation to ensure consistent output quality.**

   Example:
   ```typescript
   // In PangeaFlow agent
   import { IDENTIFY_ABSTRACTIONS_PROMPT } from "../prompts/identifyAbstractions";
   
   // Use the prompt exactly as in the original implementation
   const prompt = buildPrompt(IDENTIFY_ABSTRACTIONS_PROMPT, {
     project_name,
     context,
     max_abstractions: max_abstraction_num,
     // ...other variables
   });
   ```

2. **The new PangeaFlow agents must be saved in a separate directory structure to maintain the original PocketFlow implementation intact.**

## Directory Structure

Implement the following directory structure to maintain both implementations in parallel:

```
src/
└── Agents/
    └── Code2Documentation/
        ├── flow/                  # Original PocketFlow implementation
        │   └── flow.ts
        ├── nodes/                 # Original PocketFlow nodes
        │   ├── FetchRepo.ts
        │   ├── IdentifyAbstractions.ts
        │   └── ...
        ├── pangeaflow/            # New PangeaFlow implementation
        │   ├── agents/            # PangeaFlow agents
        │   │   ├── FetchRepoAgent.ts
        │   │   ├── IdentifyAbstractionsAgent.ts
        │   │   └── ...
        │   ├── flow/              # PangeaFlow workflow
        │   │   └── pangeaFlow.ts
        │   ├── utils/             # PangeaFlow utilities
        │   │   ├── events.ts
        │   │   └── telemetry.ts
        │   └── tests/             # PangeaFlow tests
        │       └── pangeaFlow.test.ts
        ├── prompts/               # Shared prompts (used by both implementations)
        │   ├── identifyAbstractions.ts
        │   ├── analyzeRelationships.ts
        │   └── ...
        ├── utils/                 # Shared utilities
        │   └── ...
        └── types.ts               # Shared type definitions
```

## Parallel Implementation Benefits

This parallel implementation approach allows for:

1. **Side-by-side testing and comparison** between PocketFlow and PangeaFlow
   - Run both implementations with identical inputs
   - Compare outputs for quality and consistency
   - Measure performance differences

2. **Gradual migration of users to the new system**
   - Add UI toggle in Create.tsx (as shown in section 10)
   - Monitor usage and feedback for both implementations
   - Collect metrics on performance and error rates

3. **Easy rollback if issues are encountered**
   - Keep the original implementation fully functional
   - Switch back to PocketFlow with a simple configuration change
   - No downtime required for rollbacks

4. **Performance benchmarking between the two implementations**
   - Measure execution time for each step
   - Compare memory usage
   - Analyze error rates and recovery capabilities

## Implementation Requirements

1. **Import paths must be updated** to reflect the new directory structure:
   ```typescript
   // Old (PocketFlow)
   import { FetchRepo } from '../nodes/FetchRepo';
   
   // New (PangeaFlow)
   import { FetchRepoAgent } from '../pangeaflow/agents/FetchRepoAgent';
   ```

2. **Shared utilities should be imported from common locations**:
   ```typescript
   // Both implementations
   import { buildPrompt } from "../utils/buildPrompt";
   import { IDENTIFY_ABSTRACTIONS_PROMPT } from "../prompts/identifyAbstractions";
   ```

3. **Configuration options should be consistent** between implementations:
   ```typescript
   // Both should accept the same parameters
   const shared = {
     repo_url: repoUrl,
     github_token: options.githubToken,
     // ...other parameters
   };
   ```

## Testing Strategy

1. Create unit tests for each PangeaFlow agent
2. Implement integration tests for the complete workflow
3. Create comparison tests that run both implementations and compare results
4. Measure and log performance metrics for benchmarking

## Deployment Strategy

1. Deploy both implementations simultaneously
2. Default to PocketFlow initially
3. Enable PangeaFlow for internal testing
4. Gradually roll out PangeaFlow to users with the toggle option
5. Monitor metrics and feedback
6. Switch default to PangeaFlow when stability is confirmed
7. Eventually remove PocketFlow implementation after full migration
```

