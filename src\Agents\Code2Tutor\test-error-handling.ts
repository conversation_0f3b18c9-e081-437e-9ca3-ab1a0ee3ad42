// src/Agents/Code2Tutor/test-error-handling.ts

/**
 * Test script to verify error handling improvements in Code2Tutor workflow
 */

import { executeCode2TutorFlow, createDefaultSharedStore } from './flow/pangeaFlow';
import { tutorEvents, TutorEventType } from './utils/events';

async function testErrorHandling() {
  console.log('🧪 Testing Code2Tutor error handling...');

  // Set up event listeners to capture errors
  const errorEvents: any[] = [];
  const progressEvents: any[] = [];
  const statusEvents: any[] = [];

  const errorUnsubscribe = tutorEvents.on(TutorEventType.ERROR, (error) => {
    console.log('📥 Error event captured:', error);
    errorEvents.push(error);
  });

  const progressUnsubscribe = tutorEvents.on(TutorEventType.PROGRESS, (progress) => {
    console.log('📥 Progress event captured:', progress);
    progressEvents.push(progress);
  });

  const statusUnsubscribe = tutorEvents.on(TutorEventType.AGENT_STATUS, (status) => {
    console.log('📥 Status event captured:', status);
    statusEvents.push(status);
  });

  // Also listen for global window events
  const globalErrorHandler = (event: CustomEvent) => {
    console.log('🌐 Global error event captured:', event.detail);
  };

  if (typeof window !== 'undefined') {
    window.addEventListener('code2tutor:error', globalErrorHandler as EventListener);
  }

  try {
    // Create a shared store with invalid configuration to trigger errors
    const shared = createDefaultSharedStore({
      user_id: 'test-user',
      session_id: 'test-session',
      tutorial_id: 'test-tutorial',
      
      // This should trigger an error - no repo_url or local_dir
      repo_url: undefined,
      local_dir: undefined,
      
      project_name: 'Test Project',
      target_audience: 'beginner',
      selected_files: []
    });

    console.log('🚀 Starting workflow with invalid configuration...');
    
    // Execute the workflow - this should fail and trigger error handling
    const results = await executeCode2TutorFlow(shared);
    
    console.log('📊 Workflow results:', results);
    
  } catch (error) {
    console.log('✅ Expected error caught:', error);
  }

  // Wait a bit for async events to propagate
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Clean up event listeners
  errorUnsubscribe();
  progressUnsubscribe();
  statusUnsubscribe();

  if (typeof window !== 'undefined') {
    window.removeEventListener('code2tutor:error', globalErrorHandler as EventListener);
  }

  // Report results
  console.log('\n📋 Test Results:');
  console.log(`- Error events captured: ${errorEvents.length}`);
  console.log(`- Progress events captured: ${progressEvents.length}`);
  console.log(`- Status events captured: ${statusEvents.length}`);

  if (errorEvents.length > 0) {
    console.log('\n🔍 Error event details:');
    errorEvents.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error.message || error.error?.message || 'Unknown error'}`);
      if (error.stage) {
        console.log(`     Stage: ${error.stage}`);
      }
      if (error.details) {
        console.log(`     Details:`, error.details);
      }
    });
  }

  console.log('\n✅ Error handling test completed!');
  
  return {
    errorEvents,
    progressEvents,
    statusEvents
  };
}

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  (window as any).testCode2TutorErrorHandling = testErrorHandling;
}

export { testErrorHandling };
