/**
 * Example demonstrating how to generate PNG diagrams from PangeaFlow workflows
 */

import { WorkflowBuilder } from '../pangeaflow/pangeaflow';
import { generatePangeaFlowDiagram } from '../pangeaflow/utils/flowDiagramGenerator';

// Mock LLM provider for the example
async function mockLlmProvider(prompt: string, context: Record<string, unknown>): Promise<string> {
  return `Mock response for: ${prompt.substring(0, 50)}...`;
}

// Mock tools for the example
const mockTools = {
  'file-reader': async (args: unknown) => `File content: ${JSON.stringify(args)}`,
  'web-scraper': async (args: unknown) => `Web data: ${JSON.stringify(args)}`,
  'calculator': async (args: unknown) => `Calculation result: ${JSON.stringify(args)}`,
};

/**
 * Create a sample workflow for demonstration
 */
function createSampleWorkflow() {
  const builder = WorkflowBuilder.create();

  // Add various types of agents
  builder
    .addReasoningAgent('planner', mockLlmProvider)
    .addReasoningAgent('analyzer', mockLlmProvider)
    .addReasoningAgent('reviewer', mockLlmProvider)
    .addToolAgent('file-processor', mockTools)
    .addToolAgent('web-agent', { 'web-scraper': mockTools['web-scraper'] })
    .addMemoryAgent('memory-store');

  // Define workflow routes
  builder
    .route('start', 'planner')
    .route('analyze', 'analyzer', 'file-processor')
    .route('process-files', 'file-processor')
    .route('fetch-data', 'web-agent')
    .route('review', 'reviewer', 'memory-store')
    .route('store-results', 'memory-store')
    .route('complete', 'memory-store')
    .route('error', 'planner');

  return builder.build();
}

/**
 * Create a more complex workflow with multiple branches
 */
function createComplexWorkflow() {
  const builder = WorkflowBuilder.create();

  // Add agents for a code analysis workflow
  builder
    .addReasoningAgent('code-analyzer', mockLlmProvider)
    .addReasoningAgent('concept-extractor', mockLlmProvider)
    .addReasoningAgent('tutorial-planner', mockLlmProvider)
    .addReasoningAgent('content-generator', mockLlmProvider)
    .addReasoningAgent('quality-checker', mockLlmProvider)
    .addToolAgent('repo-fetcher', { 'file-reader': mockTools['file-reader'] })
    .addToolAgent('diagram-generator', { 'calculator': mockTools['calculator'] })
    .addMemoryAgent('workflow-memory');

  // Define complex routing
  builder
    .route('start', 'repo-fetcher')
    .route('analyze-code', 'code-analyzer')
    .route('extract-concepts', 'concept-extractor')
    .route('plan-tutorial', 'tutorial-planner')
    .route('generate-content', 'content-generator', 'diagram-generator')
    .route('quality-check', 'quality-checker')
    .route('store-intermediate', 'workflow-memory')
    .route('finalize', 'workflow-memory')
    .route('retry', 'code-analyzer')
    .route('error', 'quality-checker');

  return builder.build();
}

/**
 * Generate diagrams with different styling options
 */
async function generateDiagrams() {
  console.log('🎨 Generating PangeaFlow diagrams...');

  // Create sample workflows
  const simpleWorkflow = createSampleWorkflow();
  const complexWorkflow = createComplexWorkflow();

  try {
    // Generate simple workflow diagram with default styling
    await generatePangeaFlowDiagram(
      simpleWorkflow,
      './output/pangeaflow-simple.png'
    );
    console.log('✅ Simple workflow diagram generated');

    // Generate complex workflow diagram with custom styling
    await generatePangeaFlowDiagram(
      complexWorkflow,
      './output/pangeaflow-complex.png',
      {
        width: 1400,
        height: 1000,
        backgroundColor: '#f0f4f8',
        componentColor: '#e6f3ff',
        actionColor: '#fff2e6',
        edgeColor: '#2563eb',
        textColor: '#1e293b',
        fontSize: 14,
        showComponentTypes: true,
        showActionLabels: true,
      }
    );
    console.log('✅ Complex workflow diagram generated');

    // Generate a minimal style diagram
    await generatePangeaFlowDiagram(
      simpleWorkflow,
      './output/pangeaflow-minimal.png',
      {
        width: 1000,
        height: 600,
        backgroundColor: '#ffffff',
        componentColor: '#f8fafc',
        actionColor: '#f1f5f9',
        edgeColor: '#64748b',
        textColor: '#334155',
        fontSize: 12,
        showComponentTypes: false,
        showActionLabels: false,
      }
    );
    console.log('✅ Minimal workflow diagram generated');

    // Generate a dark theme diagram
    await generatePangeaFlowDiagram(
      complexWorkflow,
      './output/pangeaflow-dark.png',
      {
        width: 1200,
        height: 900,
        backgroundColor: '#1e293b',
        componentColor: '#334155',
        actionColor: '#475569',
        edgeColor: '#60a5fa',
        textColor: '#f1f5f9',
        fontSize: 13,
        showComponentTypes: true,
        showActionLabels: true,
      }
    );
    console.log('✅ Dark theme workflow diagram generated');

    console.log('\n🎉 All PangeaFlow diagrams generated successfully!');
    console.log('📁 Check the ./output/ directory for the generated PNG files');

  } catch (error) {
    console.error('❌ Error generating diagrams:', error);
  }
}

/**
 * Example of how to use the diagram generator in your application
 */
export function exampleUsage() {
  // Create your workflow
  const workflow = createSampleWorkflow();

  // Generate a diagram
  generatePangeaFlowDiagram(workflow, './my-workflow.png', {
    width: 1200,
    height: 800,
    backgroundColor: '#f8f9fa',
    componentColor: '#e3f2fd',
    actionColor: '#e8f5e9',
    edgeColor: '#1976d2',
    showComponentTypes: true,
    showActionLabels: true,
  }).then(() => {
    console.log('Workflow diagram generated!');
  }).catch(error => {
    console.error('Failed to generate diagram:', error);
  });
}

// Run the example if this file is executed directly
// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  generateDiagrams();
}
