

/**
 * Class for managing conversation memory within a workflow
 */
export class SessionMemory<T extends Record<string, any>> {
  private memoryStore: Map<string, Partial<T>>;
  private timestamps: Map<string, number>;
  private expiryMs: number;

  /**
   * Create a new SessionMemory instance
   * @param expiryMs Time in milliseconds after which entries expire (default: 30 minutes)
   */
  constructor(expiryMs: number = 30 * 60 * 1000) {
    this.memoryStore = new Map<string, Partial<T>>();
    this.timestamps = new Map<string, number>();
    this.expiryMs = expiryMs;
  }

  /**
   * Get memory for a specific conversation
   * @param conversationId The conversation identifier
   * @returns The stored state or empty object if not found/expired
   */
  get(conversationId: string): Partial<T> {
    // Check if cache entry exists and hasn't expired
    const timestamp = this.timestamps.get(conversationId);
    if (timestamp && Date.now() - timestamp > this.expiryMs) {
      // Expired entry - clean up
      this.clear(conversationId);
      return {} as Partial<T>;
    }
    
    return this.memoryStore.get(conversationId) || {} as Partial<T>;
  }

  /**
   * Update memory for a specific conversation
   * @param conversationId The conversation identifier
   * @param state The state to store
   */
  update(conversationId: string, state: Partial<T>): void {
    this.memoryStore.set(conversationId, state);
    this.timestamps.set(conversationId, Date.now());
  }

  /**
   * Clear memory for a specific conversation
   * @param conversationId The conversation identifier
   */
  clear(conversationId: string): void {
    this.memoryStore.delete(conversationId);
    this.timestamps.delete(conversationId);
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    const now = Date.now();
    for (const [id, timestamp] of this.timestamps.entries()) {
      if (now - timestamp > this.expiryMs) {
        this.clear(id);
      }
    }
  }
  
  /**
   * Get all active conversation IDs
   * @returns Array of active conversation IDs
   */
  getActiveConversations(): string[] {
    return Array.from(this.memoryStore.keys());
  }
  
  /**
   * Check if a conversation exists in memory
   * @param conversationId The conversation identifier
   * @returns True if the conversation exists and hasn't expired
   */
  hasConversation(conversationId: string): boolean {
    const timestamp = this.timestamps.get(conversationId);
    return !!timestamp && (Date.now() - timestamp <= this.expiryMs);
  }
}

// Create a singleton instance for global use
export const globalSessionMemory = new SessionMemory<unknown>();
