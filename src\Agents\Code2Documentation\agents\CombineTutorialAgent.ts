// src/Agents/Code2Documentation/pangeaflow/agents/CombineTutorialAgent.ts


import { supabase } from "@/integrations/supabase/client";
import { BUCKET_NAME } from "@/constants";
import { callLlm_openrouter } from "@/Agents/shared/callLlm_openrouter";
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { buildPrompt } from '@/pocketflow/utils/buildPrompt';

import { emitGraphStatus, emitProgress, emitComplete } from '../utils/events';
import { Abstraction, RelationshipResult, ChapterContent } from '../types';
import { toast } from "@/hooks/use-toast";
import { PROJECT_SUMMARY_PROMPT } from "../prompts/projectSummary";



export class CombineTutorialAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super("combine-tutorial", eventBus, telemetry);
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("CombineTutorial", 0, "Starting tutorial compilation");

    try {
      // Extract parameters from context
      const {
        chapterContents,
        summary,
        project_name,
        repo_url,
        language,
        user_id,
        tutorial_id,
        abstractions,
        relationships,
        orderedAbstractions,
      } = context.sharedState;

      // Try to get data from nodeOutputs if not in sharedState
      let chaptersFromState = null;
      let abstractionsFromState = null;
      let orderedAbstractionsFromState = null;
      let relationshipsFromState = null;

      if (context.nodeOutputs.has("write-chapters")) {
        const writeChaptersResult = context.nodeOutputs.get(
          "write-chapters"
        ) as any;
        chaptersFromState = writeChaptersResult?.chapterContents;
        console.log(
          "CombineTutorialAgent: Found chapterContents in write-chapters state:",
          chaptersFromState
        );
      }

      if (context.nodeOutputs.has("identify-abstractions")) {
        const identifyAbstractionsResult = context.nodeOutputs.get(
          "identify-abstractions"
        ) as any;
        abstractionsFromState = identifyAbstractionsResult?.abstractions;
        console.log(
          "CombineTutorialAgent: Found abstractions in state:",
          abstractionsFromState
        );
      }

      if (context.nodeOutputs.has("order-chapters")) {
        const orderChaptersResult = context.nodeOutputs.get(
          "order-chapters"
        ) as any;
        orderedAbstractionsFromState = orderChaptersResult?.orderedAbstractions;
        console.log(
          "CombineTutorialAgent: Found orderedAbstractions in state:",
          orderedAbstractionsFromState
        );
      }

      if (context.nodeOutputs.has("analyze-relationships")) {
        const analyzeRelationshipsResult = context.nodeOutputs.get(
          "analyze-relationships"
        ) as any;
        relationshipsFromState = analyzeRelationshipsResult?.relationships;
        console.log(
          "CombineTutorialAgent: Found relationships in state:",
          relationshipsFromState
        );
      }

      // Use data from either source with proper fallbacks
      const chaptersData =
        (chapterContents as ChapterContent[]) || chaptersFromState || [];
      const projectName = (project_name as string) || "Unknown Project";
      const relationshipsData =
        (relationships as RelationshipResult) || relationshipsFromState;
      const abstractionsData =
        (abstractions as Abstraction[]) || abstractionsFromState || [];
      const orderedIndices =
        (orderedAbstractions as number[]) || orderedAbstractionsFromState || [];
      const repoUrl = repo_url as string;
      const lang = (language as string) || "english";

      // Validate required data
      if (!chaptersData || chaptersData.length === 0) {
        throw new Error(
          "No chapter contents found in context metadata or state"
        );
      }

      if (!abstractionsData || abstractionsData.length === 0) {
        throw new Error("No abstractions found in context metadata or state");
      }

      if (!orderedIndices || orderedIndices.length === 0) {
        throw new Error(
          "No ordered abstractions found in context metadata or state"
        );
      }

      console.log("CombineTutorialAgent: Using data:", {
        chaptersCount: chaptersData.length,
        abstractionsCount: abstractionsData.length,
        orderedIndicesCount: orderedIndices.length,
        hasRelationships: !!relationshipsData,
      });

      emitGraphStatus("CombineTutorial", 10, "Gathering tutorial components");

      // --- Generate Mermaid Diagram ---
      emitGraphStatus("CombineTutorial", 20, "Generating relationship diagram");

      const mermaidLines = ["flowchart TD"];
      // Add nodes for each abstraction
      for (let i = 0; i < abstractionsData.length; i++) {
        const nodeId = `A${i}`;
        const sanitizedName = abstractionsData[i].name.replace(/"/g, "");
        mermaidLines.push(`    ${nodeId}["${sanitizedName}"]`);
      }

      // Add edges for relationships
      if (relationshipsData && relationshipsData.details) {
        for (const rel of relationshipsData.details) {
          const fromNodeId = `A${rel.from}`;
          const toNodeId = `A${rel.to}`;
          let edgeLabel = rel.label.replace(/"/g, "").replace(/\n/g, " ");
          const maxLabelLen = 30;
          if (edgeLabel.length > maxLabelLen) {
            edgeLabel = edgeLabel.substring(0, maxLabelLen - 3) + "...";
          }
          mermaidLines.push(
            `    ${fromNodeId} -- "${edgeLabel}" --> ${toNodeId}`
          );
        }
      }

      const mermaidDiagram = mermaidLines.join("\n");

      // --- Prepare index.md content ---
      emitGraphStatus("CombineTutorial", 30, "Creating index page");

      let indexContent = `# Tutorial: ${projectName}\n\n`;
      if (summary) {
        indexContent += `${summary}\n\n`;
      }

      if (repoUrl) {
        indexContent += `**Source Repository:** [${repoUrl}](${repoUrl})\n\n`;
      }

      // Add Mermaid diagram for relationships
      indexContent += "```mermaid\n";
      indexContent += mermaidDiagram + "\n";
      indexContent += "```\n\n";

      indexContent += `## Chapters\n\n`;

      emitGraphStatus("CombineTutorial", 40, "Preparing chapter files");

      const chapterFiles = [];
      // Generate chapter links based on the determined order
      for (let i = 0; i < orderedIndices.length; i++) {
        const abstractionIndex = orderedIndices[i];
        if (
          0 <= abstractionIndex &&
          abstractionIndex < abstractionsData.length &&
          i < chaptersData.length
        ) {
          const abstractionName = abstractionsData[abstractionIndex].name;
          const safeName = abstractionName
            .replace(/[^a-zA-Z0-9]/g, "_")
            .toLowerCase();
          const filename = `${(i + 1)
            .toString()
            .padStart(2, "0")}_${safeName}.md`;
          indexContent += `${i + 1}. [${abstractionName}](${filename})\n`;

          // Add attribution to chapter content
          let chapterContent = chaptersData[i].content;
          if (!chapterContent.endsWith("\n\n")) {
            chapterContent += "\n\n";
          }
          chapterContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

          chapterFiles.push({ filename, content: chapterContent });
        } else {
          emitGraphStatus(
            "CombineTutorial",
            45,
            `Warning: Mismatch at index ${i} (abstraction index ${abstractionIndex})`
          );
          console.log(
            `Warning: Mismatch between chapter order, abstractions, or content at index ${i} (abstraction index ${abstractionIndex}). Skipping file generation for this entry.`
          );
        }
      }

      // Add attribution to index content
      indexContent += `---\n\nGenerated by [Generative Pangea](https://www.generativepangea.com) - AI-powered documentation solutions`;

      emitGraphStatus(
        "CombineTutorial",
        50,
        "Preparation complete, ready to write files"
      );

      // Save to Supabase
      const tutorialDBId = await this.saveToSupabase(
        tutorial_id as string,
        indexContent,
        chapterFiles,
        projectName,
        relationshipsData?.summary || "",
        repoUrl,
        lang,
        user_id as string
      );

      // Combine all chapters into a single tutorial string
      let combinedTutorial = indexContent + "\n\n";
      chapterFiles.forEach((chapter) => {
        combinedTutorial += `\n\n${chapter.content}\n\n`;
      });

      emitGraphStatus(
        "CombineTutorial",
        95,
        `Tutorial saved with ID: ${tutorialDBId}`
      );

      // Emit progress event
      emitProgress(
        "Tutorial Compilation",
        100,
        `Tutorial generated successfully`
      );

      // Final graph status
      emitGraphStatus("CombineTutorial", 100, "Tutorial compilation complete");

      // Emit complete event
      emitComplete({
        success: true,
        message: "Tutorial successfully generated",
        tutorialId: tutorialDBId,
      });

      // Return success result
      return {
        success: true,
        output: {
          tutorial: combinedTutorial,
          project_name: projectName,
          summary: relationshipsData?.summary,
          tutorialId: tutorialDBId,
          indexContent,
          chapterFiles,
        },
        events: [],
        nextActions: ["complete"],
        sharedStateUpdates: {
          ...context.sharedState,
          tutorial: combinedTutorial,
          final_output_dir: tutorialDBId,
        },
      };
    } catch (error) {
      // Return error result
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ["error"],
        sharedStateUpdates: context.sharedState,
      };
    }
  }

  private async saveToSupabase(
    tutorialId: string,
    indexContent: string,
    chapterFiles: any[],
    projectName: string,
    project_description: string,
    repoUrl: string,
    language: string,
    user_id: string
  ): Promise<string> {
    emitGraphStatus(
      "CombineTutorial",
      60,
      `Preparing to save tutorial to Supabase`
    );

    if (!supabase) {
      throw new Error(
        "Supabase client not initialized. Check your environment variables."
      );
    }

    try {
      // Save the index file to Supabase Storage
      emitGraphStatus("CombineTutorial", 70, "Saving index.md file");
      const indexPath = `${tutorialId}/index.md`;
      const indexBlob = new Blob([indexContent], { type: "text/markdown" });

      const { error: indexError } = await supabase.storage
        .from(BUCKET_NAME)
        .upload(indexPath, indexBlob, {
          contentType: "text/markdown",
          upsert: true,
        });

      if (indexError) {
        console.error("Error uploading index.md:", indexError);
        throw indexError;
      }

      console.log(`Saved index.md to Supabase: ${indexPath}`);

      // Get public URL for the index file
      const { data: indexUrlData } = supabase.storage
        .from("tutorials")
        .getPublicUrl(indexPath);

      const indexUrl = indexUrlData.publicUrl;

      // Save chapter files
      emitGraphStatus(
        "CombineTutorial",
        80,
        `Saving ${chapterFiles.length} chapter files`
      );
      const chapterUrls = [];

      for (const chapterInfo of chapterFiles) {
        const chapterPath = `${tutorialId}/${chapterInfo.filename}`;
        const chapterBlob = new Blob([chapterInfo.content], {
          type: "text/markdown",
        });

        const { error: chapterError } = await supabase.storage
          .from("tutorials")
          .upload(chapterPath, chapterBlob, {
            contentType: "text/markdown",
            upsert: true,
          });

        if (chapterError) {
          console.error(
            `Error uploading chapter ${chapterInfo.filename}:`,
            chapterError
          );
          throw chapterError;
        }

        console.log(`Saved chapter to Supabase: ${chapterPath}`);

        const { data: chapterUrlData } = supabase.storage
          .from("tutorials")
          .getPublicUrl(chapterPath);

        chapterUrls.push({
          filename: chapterInfo.filename,
          url: chapterUrlData.publicUrl,
        });
      }

      // Save tutorial metadata to database
      let tutorialDBId: string;
      try {
        const prompt = buildPrompt(PROJECT_SUMMARY_PROMPT, {
          project_description,
        });

        const llm_project_summary = await callLlm_openrouter({
          tutorial_id: tutorialId,
          prompt,
          use_cache: true,
          temperature: 0.7,
          model: "google/gemma-3n-e4b-it:free",
          user_id,
        });

        emitGraphStatus("CombineTutorial", 85, "Generating project summary");

        const { data: metaData, error: metaError } = await supabase
          .from("tutorial_metadata")
          .insert({
            tutorial_id: tutorialId,
            project_name: projectName,
            index_url: indexUrl,
            chapter_urls: chapterUrls,
            description: llm_project_summary,
            repo_url: repoUrl,
            language: language,
            user_id: user_id || null,
          })
          .select();

        if (metaError) {
          console.error("Error saving to tutorial_metadata:", metaError);
          throw metaError;
        }
        tutorialDBId = metaData[0].id;


        // Generate cover image
        emitGraphStatus("CombineTutorial", 90, "Generating tutorial cover image");
        let hasCoverError = false;
        try {
          const { data: coverData, error: coverError } =
            await supabase.functions.invoke("generate-tutorial-cover", {
              body: {
                tutorialId: tutorialId,
                projectName: projectName,
                description: llm_project_summary,
                language: language,
              },
            });

          if (coverError) {
            console.error("Error generating cover image:", coverError);
            hasCoverError = true;
            toast({
              title: "Cover Generation Warning",
              description:
                "Tutorial saved successfully but cover image generation failed",
              variant: "default",
            });
          } else {
            console.log("Cover image generated successfully:", coverData);
          }
        } catch (coverErr) {
          console.error("Error in cover generation:", coverErr);
          hasCoverError = true;
        }
          // Success toast notification (only if cover generation succeeded)
        if (!hasCoverError) {
          toast({
            title: "Tutorial Complete",
            description: "Tutorial and cover image generated successfully",
          });
        } else {
          toast({
            title: "Tutorial Saved",
            description: "Tutorial files and metadata saved successfully",
          });
        }


        console.log("Tutorial metadata saved successfully");
      } catch (metaErr) {
        console.error("Error in metadata saving:", metaErr);
        // Continue without metadata if files are saved
      }

      emitGraphStatus(
        "CombineTutorial",
        95,
        "All files saved to Supabase successfully"
      );
      return tutorialDBId;
    } catch (error) {
      console.error("Error saving to Supabase:", error);
      throw error;
    }
  }
}
