
import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';

interface SubscriptionData {
  subscribed: boolean;
  subscription_tier?: string;
  subscription_end?: string;
  trial_end?: string;
  in_trial?: boolean;
}

// Global request deduplication to prevent multiple simultaneous calls
let globalSubscriptionPromise: Promise<any> | null = null;

export const useSubscription = () => {
  const { user, session } = useAuth();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    subscribed: false
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const isCheckingRef = useRef(false);

  const checkSubscription = useCallback(async () => {
    if (!session?.access_token) return;

    // Prevent multiple simultaneous calls
    if (isCheckingRef.current) {
      console.log('Subscription check already in progress, skipping...');
      return;
    }

    // Use global promise deduplication
    if (globalSubscriptionPromise) {
      console.log('Using existing subscription check promise...');
      try {
        const result = await globalSubscriptionPromise;
        setSubscriptionData(result);
        setError(null);
      } catch (err) {
        console.error('Error from shared subscription check:', err);
        setError(err instanceof Error ? err.message : 'Failed to check subscription');
      } finally {
        setLoading(false);
      }
      return;
    }

    try {
      setLoading(true);
      isCheckingRef.current = true;

      // Create the promise and store it globally
      globalSubscriptionPromise = supabase.functions.invoke('check-subscription', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      const { data, error } = await globalSubscriptionPromise;

      if (error) throw error;
      setSubscriptionData(data);
      setError(null);
    } catch (err) {
      console.error('Error checking subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to check subscription');
    } finally {
      setLoading(false);
      isCheckingRef.current = false;
      globalSubscriptionPromise = null; // Clear the global promise
    }
  }, [session?.access_token]);

  useEffect(() => {
    if (user && session) {
      checkSubscription();
    } else {
      setLoading(false);
    }
  }, [user, session, checkSubscription]);

  return {
    ...subscriptionData,
    loading,
    error,
    refetchSubscription: checkSubscription,
  };
};
