import { createSimpleWorkflow, runSimpleWorkflow } from '../pangeaflow/simple';

// Example LLM provider
const llmProvider = async (prompt: string) => {
  return `Response to: ${prompt}`;
};

// Example tools
const tools = {
  'fetch-data': async (url: string) => {
    return { data: 'Example data' };
  }
};

// Create a simple workflow
const workflow = createSimpleWorkflow({
  agents: [
    { id: 'planner', type: 'reasoning', handler: llmProvider },
    { id: 'executor', type: 'tool', handler: tools },
    { id: 'storage', type: 'memory' }
  ],
  routes: [
    { from: 'start', to: 'planner' },
    { from: 'execute', to: 'executor' },
    { from: 'store', to: 'storage' }
  ]
});

// Run the workflow
async function main() {
  const result = await runSimpleWorkflow(workflow, 'start', {
    input: 'Analyze this code'
  });
  
  console.log('Workflow result:', result);
}

main();