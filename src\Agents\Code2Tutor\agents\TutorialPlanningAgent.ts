// src/Agents/Code2Tutor/agents/TutorialPlanningAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow/pangeaflow';
import { SharedStore, TutorialStructure, TutorialMetadata, ConceptRelationship } from '../types';
import { emitAgentStatus, emitTutorProgress } from '../utils/events';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';
import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';
import { TUTORIAL_PLANNING_PROMPT } from '../prompts';
import yaml from 'js-yaml';

/**
 * TutorialPlanningAgent - Plans the overall tutorial structure and learning progression
 * 
 * This agent is responsible for:
 * - Creating tutorial metadata and learning objectives
 * - Organizing concepts into a logical learning progression
 * - Defining relationships between concepts
 * - Planning individual tutorial sections
 * - Estimating completion times and difficulty levels
 */
export class TutorialPlanningAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 3) {
    super('tutorial-planning', eventBus, telemetry, {
      stage: 'tutorial-planning',
      progress: 0
    });
    this.maxRetries = maxRetries;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('tutorial-planning', async () => {
      emitAgentStatus('TutorialPlanningAgent', 'starting', 0, 'Initializing tutorial planning');
      
      const shared = context.sharedState.shared as SharedStore;
      
      try {
        // Validate inputs
        if (!shared.concepts || shared.concepts.length === 0) {
          throw new Error('No concepts available for tutorial planning');
        }

        emitAgentStatus('TutorialPlanningAgent', 'processing', 10, 'Analyzing concept relationships');
        emitTutorProgress('Tutorial Planning', 10, 'Designing tutorial structure');

        // Prepare concepts for LLM analysis
        const conceptsYaml = this.prepareConceptsYaml(shared.concepts);

        emitAgentStatus('TutorialPlanningAgent', 'processing', 30, 'Generating tutorial structure with LLM');

        // Build prompt for tutorial planning
        const prompt = buildPrompt(TUTORIAL_PLANNING_PROMPT, {
          project_name: shared.project_name || 'Unknown Project',
          concepts_yaml: conceptsYaml,
          target_audience: shared.target_audience || 'beginner',
          tutorial_format: shared.tutorial_format || 'guided',
          include_exercises: shared.include_exercises ? 'Yes' : 'No',
          include_diagrams: shared.include_diagrams ? 'Yes' : 'No',
          language_instruction: this.getLanguageInstruction(shared.content_language)
        });

        emitAgentStatus('TutorialPlanningAgent', 'processing', 50, 'Analyzing optimal learning progression');

        // Call LLM for tutorial planning
        const response = await callLlm_openrouter({
          tutorial_id: shared.tutorial_id,
          prompt,
          temperature: 0.5, // Lower temperature for more structured planning
          model: "google/gemini-2.5-flash-preview-05-20",
          use_cache: shared.use_cache && this.currentRetry === 0,
          user_id: shared.user_id
        });

        emitAgentStatus('TutorialPlanningAgent', 'processing', 70, 'Processing tutorial structure');

        // Parse the tutorial structure from response
        const tutorialStructure = this.parseTutorialStructure(response, shared.concepts);

        emitAgentStatus('TutorialPlanningAgent', 'processing', 85, 'Validating tutorial progression');

        // Validate and enhance the tutorial structure
        const validatedStructure = this.validateTutorialStructure(tutorialStructure, shared);

        // Update shared store
        shared.tutorial_structure = validatedStructure;

        emitAgentStatus('TutorialPlanningAgent', 'completed', 100, `Tutorial structure planned with ${validatedStructure.sections.length} sections`);
        emitTutorProgress('Tutorial Planning', 100, `Tutorial structure ready with ${validatedStructure.sections.length} sections`);

        this.emit('tutorial.planned', {
          structure: validatedStructure,
          sectionCount: validatedStructure.sections.length,
          estimatedTime: validatedStructure.metadata.estimatedTime
        }, context.id);

        return {
          success: true,
          output: {
            tutorialStructure: validatedStructure,
            sectionCount: validatedStructure.sections.length
          },
          events: [],
          nextActions: ['generate-content'],
          sharedStateUpdates: {
            // ✅ Preserve existing shared state and add new data
            ...context.sharedState,
            tutorial_structure: validatedStructure,
            sectionsPlanned: validatedStructure.sections.length,
            estimatedTime: validatedStructure.metadata.estimatedTime,
            targetAudience: validatedStructure.metadata.targetAudience
          }
        };

      } catch (error) {
        this.currentRetry++;
        
        if (this.currentRetry < this.maxRetries) {
          emitAgentStatus('TutorialPlanningAgent', 'processing', 0, `Retry ${this.currentRetry}/${this.maxRetries}: ${error.message}`);
          
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000 * this.currentRetry));
          
          // Retry execution
          return this.execute(context);
        }

        emitAgentStatus('TutorialPlanningAgent', 'error', 0, `Failed after ${this.maxRetries} attempts: ${error.message}`);
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          sharedStateUpdates: { stage: 'tutorial-planning', retries: this.currentRetry }
        };
      }
    });
  }

  /**
   * Prepare concepts as YAML for LLM analysis
   */
  private prepareConceptsYaml(concepts: any[]): string {
    const conceptsData = {
      concepts: concepts.map(concept => ({
        name: concept.name,
        description: concept.description,
        difficulty: concept.difficulty,
        prerequisites: concept.prerequisites,
        examples: concept.examples
      }))
    };

    return yaml.dump(conceptsData);
  }

  /**
   * Parse tutorial structure from LLM response
   */
  private parseTutorialStructure(response: string, concepts: any[]): TutorialStructure {
    try {
      // Extract YAML from response
      const yamlMatch = response.match(/```yaml\s*([\s\S]*?)\s*```/);
      if (!yamlMatch) {
        throw new Error('No YAML block found in response');
      }

      const yamlContent = yamlMatch[1];
      const parsed = yaml.load(yamlContent) as any;

      // Extract tutorial metadata
      const metadata: TutorialMetadata = {
        title: parsed.tutorial_metadata?.title || 'Code Tutorial',
        description: parsed.tutorial_metadata?.description || 'Learn to code with this tutorial',
        targetAudience: parsed.tutorial_metadata?.target_audience || 'beginner',
        estimatedTime: parsed.tutorial_metadata?.estimated_time || 60,
        prerequisites: parsed.tutorial_metadata?.prerequisites || [],
        learningObjectives: parsed.tutorial_metadata?.learning_objectives || []
      };

      // Extract concept relationships
      const relationships: ConceptRelationship[] = (parsed.concept_relationships || []).map((rel: any) => ({
        from: rel.from,
        to: rel.to,
        type: rel.type || 'related',
        strength: rel.strength || 0.5
      }));

      // Extract progression path
      const progressionPath: string[] = parsed.progression_path || concepts.map((_, index) => `concept_${index}`);

      // Extract sections
      const sections = (parsed.sections || []).map((section: any, index: number) => ({
        id: section.id || `section_${index}`,
        title: section.title || `Section ${index + 1}`,
        concept: section.concept || concepts[index]?.name || 'Unknown Concept',
        content: '', // Will be filled by ContentGenerationAgent
        exercises: [], // Will be filled by ContentGenerationAgent
        codeExamples: [] // Will be filled by ContentGenerationAgent
      }));

      return {
        metadata,
        concepts,
        relationships,
        sections,
        progressionPath
      };

    } catch (error) {
      console.error('Error parsing tutorial structure:', error);
      throw new Error(`Failed to parse tutorial structure: ${error.message}`);
    }
  }

  /**
   * Validate and enhance tutorial structure
   */
  private validateTutorialStructure(structure: TutorialStructure, shared: SharedStore): TutorialStructure {
    // Ensure we have at least one section
    if (structure.sections.length === 0) {
      structure.sections = shared.concepts!.map((concept, index) => ({
        id: `section_${index}`,
        title: `Learning ${concept.name}`,
        concept: concept.name,
        content: '',
        exercises: [],
        codeExamples: []
      }));
    }

    // Ensure progression path matches sections
    if (structure.progressionPath.length !== structure.sections.length) {
      structure.progressionPath = structure.sections.map(section => section.id);
    }

    // Validate estimated time (minimum 15 minutes, maximum 8 hours)
    structure.metadata.estimatedTime = Math.max(15, Math.min(480, structure.metadata.estimatedTime));

    // Ensure target audience matches shared store
    structure.metadata.targetAudience = shared.target_audience || structure.metadata.targetAudience;

    return structure;
  }

  /**
   * Get language instruction based on content language
   */
  private getLanguageInstruction(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return `Write all content in ${language}. `;
    }
    return '';
  }
}
