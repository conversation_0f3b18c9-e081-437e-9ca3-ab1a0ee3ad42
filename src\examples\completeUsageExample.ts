/**
 * Complete usage example for PangeaFlow PNG diagram generation
 * 
 * This example demonstrates:
 * 1. Creating a workflow
 * 2. Generating PNG diagrams with different styles
 * 3. Using the utility in different scenarios
 */

import { 
  WorkflowBuilder, 
  generatePangeaFlowDiagram,
  type PangeaFlowDiagramOptions 
} from '../pangeaflow/pangeaflow';

// Mock LLM provider
async function mockLlm(prompt: string): Promise<string> {
  return `AI Response: ${prompt.substring(0, 30)}...`;
}

// Mock tools
const tools = {
  'file-reader': async (args: any) => `File: ${JSON.stringify(args)}`,
  'api-caller': async (args: any) => `API: ${JSON.stringify(args)}`,
  'data-processor': async (args: any) => `Processed: ${JSON.stringify(args)}`,
};

/**
 * Example 1: Simple workflow diagram
 */
async function example1_SimpleWorkflow() {
  console.log('\n📊 Example 1: Simple Workflow');
  
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('planner', mockLlm)
    .addToolAgent('executor', tools)
    .addMemoryAgent('storage')
    .route('start', 'planner')
    .route('execute', 'executor')
    .route('store', 'storage')
    .build();

  await generatePangeaFlowDiagram(
    workflow,
    './output/example1-simple.png'
  );
  
  console.log('✅ Generated: ./output/example1-simple.png');
}

/**
 * Example 2: Complex workflow with custom styling
 */
async function example2_StyledWorkflow() {
  console.log('\n🎨 Example 2: Styled Workflow');
  
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('analyzer', mockLlm)
    .addReasoningAgent('reviewer', mockLlm)
    .addReasoningAgent('optimizer', mockLlm)
    .addToolAgent('data-fetcher', { 'file-reader': tools['file-reader'] })
    .addToolAgent('api-client', { 'api-caller': tools['api-caller'] })
    .addToolAgent('processor', { 'data-processor': tools['data-processor'] })
    .addMemoryAgent('cache')
    .addMemoryAgent('results')
    .route('start', 'analyzer')
    .route('fetch-data', 'data-fetcher', 'api-client')
    .route('process', 'processor')
    .route('review', 'reviewer')
    .route('optimize', 'optimizer')
    .route('cache-intermediate', 'cache')
    .route('store-final', 'results')
    .route('retry', 'analyzer')
    .build();

  const customStyle: PangeaFlowDiagramOptions = {
    width: 1400,
    height: 1000,
    backgroundColor: '#f0f4f8',
    componentColor: '#e6f3ff',
    actionColor: '#fff2e6',
    edgeColor: '#2563eb',
    textColor: '#1e293b',
    fontSize: 13,
    padding: 60,
    showComponentTypes: true,
    showActionLabels: true,
  };

  await generatePangeaFlowDiagram(
    workflow,
    './output/example2-styled.png',
    customStyle
  );
  
  console.log('✅ Generated: ./output/example2-styled.png');
}

/**
 * Example 3: Multiple theme variations
 */
async function example3_MultipleThemes() {
  console.log('\n🌈 Example 3: Multiple Themes');
  
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('coordinator', mockLlm)
    .addReasoningAgent('specialist', mockLlm)
    .addToolAgent('worker', tools)
    .addMemoryAgent('shared-state')
    .route('start', 'coordinator')
    .route('delegate', 'specialist')
    .route('execute', 'worker')
    .route('sync', 'shared-state')
    .build();

  // Light theme
  await generatePangeaFlowDiagram(workflow, './output/example3-light.png', {
    backgroundColor: '#ffffff',
    componentColor: '#f8fafc',
    actionColor: '#f1f5f9',
    edgeColor: '#64748b',
    textColor: '#334155',
  });

  // Dark theme
  await generatePangeaFlowDiagram(workflow, './output/example3-dark.png', {
    backgroundColor: '#1e293b',
    componentColor: '#334155',
    actionColor: '#475569',
    edgeColor: '#60a5fa',
    textColor: '#f1f5f9',
  });

  // Minimal theme
  await generatePangeaFlowDiagram(workflow, './output/example3-minimal.png', {
    backgroundColor: '#ffffff',
    componentColor: '#f9fafb',
    actionColor: '#f3f4f6',
    edgeColor: '#9ca3af',
    textColor: '#374151',
    showComponentTypes: false,
    showActionLabels: false,
  });
  
  console.log('✅ Generated: Light, Dark, and Minimal themes');
}

/**
 * Example 4: Different sizes
 */
async function example4_DifferentSizes() {
  console.log('\n📏 Example 4: Different Sizes');
  
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('brain', mockLlm)
    .addToolAgent('hands', tools)
    .addMemoryAgent('memory')
    .route('think', 'brain')
    .route('act', 'hands')
    .route('remember', 'memory')
    .build();

  // Small size (thumbnail)
  await generatePangeaFlowDiagram(workflow, './output/example4-small.png', {
    width: 600,
    height: 400,
    fontSize: 10,
    padding: 30,
  });

  // Medium size (standard)
  await generatePangeaFlowDiagram(workflow, './output/example4-medium.png', {
    width: 1000,
    height: 700,
    fontSize: 12,
    padding: 50,
  });

  // Large size (detailed)
  await generatePangeaFlowDiagram(workflow, './output/example4-large.png', {
    width: 1600,
    height: 1200,
    fontSize: 16,
    padding: 80,
  });
  
  console.log('✅ Generated: Small, Medium, and Large sizes');
}

/**
 * Example 5: Frontend integration pattern
 */
async function example5_FrontendPattern() {
  console.log('\n🌐 Example 5: Frontend Integration Pattern');
  
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('ui-controller', mockLlm)
    .addToolAgent('api-handler', tools)
    .addMemoryAgent('state-manager')
    .route('user-action', 'ui-controller')
    .route('api-call', 'api-handler')
    .route('update-state', 'state-manager')
    .build();

  // Generate a diagram optimized for web display
  await generatePangeaFlowDiagram(workflow, './output/example5-web.png', {
    width: 1200,
    height: 600,
    backgroundColor: '#ffffff',
    componentColor: '#f8f9fa',
    actionColor: '#e9ecef',
    edgeColor: '#495057',
    textColor: '#212529',
    fontSize: 12,
    padding: 40,
    showComponentTypes: true,
    showActionLabels: true,
  });
  
  console.log('✅ Generated: Web-optimized diagram');
  console.log('💡 Use this in your frontend: <img src="/diagrams/example5-web.png" alt="Workflow" />');
}

/**
 * Run all examples
 */
async function runAllExamples() {
  console.log('🚀 Running PangeaFlow diagram generation examples...');
  
  try {
    await example1_SimpleWorkflow();
    await example2_StyledWorkflow();
    await example3_MultipleThemes();
    await example4_DifferentSizes();
    await example5_FrontendPattern();
    
    console.log('\n🎉 All examples completed successfully!');
    console.log('\n📁 Generated files in ./output/:');
    console.log('   - example1-simple.png');
    console.log('   - example2-styled.png');
    console.log('   - example3-light.png');
    console.log('   - example3-dark.png');
    console.log('   - example3-minimal.png');
    console.log('   - example4-small.png');
    console.log('   - example4-medium.png');
    console.log('   - example4-large.png');
    console.log('   - example5-web.png');
    
    console.log('\n💡 Usage tips:');
    console.log('   • Use small sizes for thumbnails and previews');
    console.log('   • Use dark themes for dark mode interfaces');
    console.log('   • Use minimal themes for clean, simple displays');
    console.log('   • Disable labels for compact representations');
    
  } catch (error) {
    console.error('❌ Error running examples:', error);
  }
}

// Export for use in other files
export {
  example1_SimpleWorkflow,
  example2_StyledWorkflow,
  example3_MultipleThemes,
  example4_DifferentSizes,
  example5_FrontendPattern,
  runAllExamples,
};

// Run if executed directly
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  runAllExamples();
}
