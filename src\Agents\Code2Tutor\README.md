# Code2Tutor Agent

The Code2Tutor agent transforms code repositories into comprehensive, interactive educational tutorials. Unlike traditional documentation generators, Code2Tutor focuses on creating learning-oriented content that helps developers understand and master new technologies through hands-on practice.

## Architecture

Code2Tutor uses the **PangeaFlow** architecture (LangGraph-inspired) for reactive, event-driven workflow orchestration. This provides several advantages over traditional linear approaches:

- **Event-driven**: Loose coupling between components
- **Reactive**: Automatic state management and invalidation
- **Observable**: Built-in telemetry and monitoring
- **Resilient**: Error recovery and retry mechanisms
- **Scalable**: Streaming support for large repositories

## Workflow Stages

### 1. Repository Analysis (`RepoAnalysisAgent`)
- Fetches files from GitHub repositories or local directories
- Filters files for educational relevance
- Analyzes repository structure and programming languages
- Prepares code context for concept extraction

### 2. Concept Extraction (`ConceptExtractionAgent`)
- Uses LLM to identify key learning concepts from code
- Determines concept difficulty levels and prerequisites
- Maps concepts to relevant code files
- Generates practical examples for each concept

### 3. Tutorial Planning (`TutorialPlanningAgent`)
- Creates tutorial metadata and learning objectives
- Organizes concepts into logical learning progression
- Defines relationships between concepts
- Plans individual tutorial sections with estimated times

### 4. Content Generation (`ContentGenerationAgent`)
- Generates detailed educational content for each section
- Creates hands-on exercises and coding challenges
- Develops code examples with explanations
- Ensures content is appropriate for target audience

### 5. Tutorial Assembly (`TutorialAssemblyAgent`)
- Combines all sections into a cohesive tutorial
- Creates table of contents and navigation
- Adds introduction and conclusion sections
- Saves tutorial to storage and generates metadata

## Key Features

### Educational Focus
- **Learning Outcomes**: Content designed around specific learning objectives
- **Progressive Difficulty**: Concepts build upon each other logically
- **Hands-on Practice**: Interactive exercises and coding challenges
- **Real-world Context**: Examples drawn from actual codebase usage

### Multi-Audience Support
- **Beginner**: Fundamental concepts with extensive explanations
- **Intermediate**: Practical applications and best practices
- **Advanced**: Complex patterns and optimization techniques

### Interactive Elements
- **Coding Exercises**: Hands-on programming challenges
- **Quizzes**: Knowledge verification questions
- **Code Explanations**: Analyze and understand existing code
- **Debugging Challenges**: Find and fix issues in provided code

### Visual Learning
- **Mermaid Diagrams**: Illustrate complex concepts and relationships
- **Code Highlighting**: Syntax-highlighted examples
- **Progress Indicators**: Track learning progress through sections

## Usage

### Basic Usage

```typescript
import { executeCode2TutorFlow, createDefaultSharedStore } from '@/Agents/Code2Tutor';

const shared = createDefaultSharedStore({
  user_id: 'user123',
  repo_url: 'https://github.com/example/repo',
  project_name: 'React Tutorial Project',
  target_audience: 'beginner',
  include_exercises: true,
  include_diagrams: true
});

const results = await executeCode2TutorFlow(shared);
console.log('Tutorial created:', results);
```

### Configuration Options

```typescript
interface SharedStore {
  // Repository
  repo_url?: string;
  local_dir?: string;
  project_name?: string;
  github_token?: string;

  // Tutorial Settings
  target_audience: 'beginner' | 'intermediate' | 'advanced';
  content_language: string;
  tutorial_format: 'interactive' | 'guided' | 'self-paced';
  include_exercises: boolean;
  include_diagrams: boolean;
  max_concepts: number;

  // User Context
  user_id: string;
  session_id?: string;
}
```

### Event Monitoring

```typescript
import { tutorEvents, TutorEventType } from '@/Agents/Code2Tutor';

// Monitor progress
tutorEvents.on(TutorEventType.PROGRESS, (event) => {
  console.log(`Progress: ${event.progress}% - ${event.message}`);
});

// Monitor agent status
tutorEvents.on(TutorEventType.AGENT_STATUS, (event) => {
  console.log(`${event.agentName}: ${event.status} - ${event.message}`);
});

// Handle completion
tutorEvents.on(TutorEventType.COMPLETE, (event) => {
  if (event.success) {
    console.log('Tutorial completed:', event.tutorialUrl);
  } else {
    console.error('Tutorial failed:', event.message);
  }
});
```

## Comparison with Code2Documentation

| Feature | Code2Documentation | Code2Tutor |
|---------|-------------------|-------------|
| **Purpose** | Technical documentation | Educational tutorials |
| **Architecture** | PocketFlow (linear) | PangeaFlow (reactive) |
| **Content Focus** | API reference, structure | Learning concepts, practice |
| **Target Audience** | Developers familiar with code | Learners at various levels |
| **Interactivity** | Static documentation | Exercises, quizzes, challenges |
| **Progression** | Logical code organization | Pedagogical learning path |
| **Examples** | Code snippets | Hands-on tutorials |

## Supported Languages

- JavaScript/TypeScript
- Python
- Java
- Go
- Rust
- C/C++
- C#
- PHP
- Ruby

## Output Format

The Code2Tutor agent generates:

1. **Interactive Tutorial**: Complete markdown tutorial with exercises
2. **Metadata**: Learning objectives, prerequisites, estimated time
3. **Progress Tracking**: Section completion and learning milestones
4. **Exercise Solutions**: Detailed explanations and code solutions
5. **Visual Aids**: Diagrams and illustrations for complex concepts

## Integration

The Code2Tutor agent integrates seamlessly with the existing application:

- **UI Components**: Reuses existing progress tracking and status displays
- **Storage**: Saves tutorials to Supabase with metadata
- **Authentication**: Respects user permissions and access controls
- **Caching**: Leverages LLM response caching for efficiency

## Future Enhancements

- **Adaptive Learning**: Adjust difficulty based on user progress
- **Code Execution**: Run code examples in sandboxed environments
- **Collaborative Features**: Share tutorials and collaborate on improvements
- **Analytics**: Track learning effectiveness and optimize content
- **Multi-modal**: Support for video explanations and audio narration
