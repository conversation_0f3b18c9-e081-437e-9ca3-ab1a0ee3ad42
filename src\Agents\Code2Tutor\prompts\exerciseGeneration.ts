export const EXERCISE_GENERATION_PROMPT = `\${language_instruction}Create engaging, hands-on exercises for the concept "\${concept_name}" in the project \${project_name}.

Concept Details:
\${concept_description}

Target Audience: \${target_audience}
Code Context:
\${code_context}

Create 2-4 exercises that help learners practice and reinforce this concept. Include a mix of exercise types:

1. **Coding Exercise** - Hands-on programming challenge
2. **Quiz Questions** - Multiple choice or short answer to test understanding  
3. **Code Explanation** - Analyze and explain existing code
4. **Debugging Challenge** - Find and fix issues in provided code

For each exercise, provide:

**Exercise Structure:**
- **Type**: coding|quiz|explanation|debugging
- **Title**: Engaging, descriptive title
- **Description**: Clear instructions (2-3 sentences)
- **Difficulty**: easy|medium|hard (appropriate for \${target_audience})
- **Estimated Time**: Minutes to complete
- **Starter Code**: If applicable, provide starting code
- **Expected Solution**: Complete solution or correct answer
- **Hints**: 2-3 progressive hints to help struggling learners
- **Learning Objective**: What specific skill this exercise develops

**Guidelines:**
- Make exercises practical and relevant to real-world usage
- Start with easier exercises and build complexity
- Provide clear, achievable goals
- Include realistic scenarios from the codebase
- Ensure exercises can be completed with the knowledge taught
- Make hints helpful without giving away the solution

**Example Output Format:**

\`\`\`yaml
exercises:
  - type: "coding"
    title: "Build Your First [Concept] Implementation"
    description: "Create a simple [concept] that demonstrates the core functionality we just learned."
    difficulty: "easy"
    estimated_time: 15
    starter_code: |
      // Your starting code here
      function example() {
        // TODO: Implement the concept
      }
    expected_solution: |
      // Complete solution
      function example() {
        // Implementation details
      }
    hints:
      - "Remember to initialize the [key component] first"
      - "Check the pattern we used in the previous example"
      - "The solution should follow the [specific pattern] structure"
    learning_objective: "Practice implementing [concept] from scratch"

  - type: "quiz"
    title: "Understanding [Concept] Behavior"
    description: "Test your understanding of how [concept] works in different scenarios."
    difficulty: "medium"
    estimated_time: 5
    questions:
      - question: "What happens when you [specific scenario]?"
        options:
          - "Option A"
          - "Option B" 
          - "Option C"
        correct_answer: "Option B"
        explanation: "Detailed explanation of why this is correct"
    learning_objective: "Verify understanding of [concept] behavior"
\`\`\`

Create exercises that are:
- Engaging and motivating
- Directly applicable to the codebase
- Appropriately challenging for \${target_audience}
- Clear in their instructions and expectations
- Supportive with helpful hints and feedback`;


