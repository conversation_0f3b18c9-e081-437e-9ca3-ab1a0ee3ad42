// src/Agents/Code2Documentation/pangeaflow/flow/pangeaFlow.ts

  import {
  WorkflowBuilder,
  WorkflowOrchestrator,
  EventBus,
  TelemetryCollector
} from '@/pangeaflow';
import { SharedStore } from '../types';
import { 
  FetchRepoAgent,
  IdentifyAbstractionsAgent,
  AnalyzeRelationshipsAgent,
  OrderChaptersAgent,
  WriteChaptersAgent,
  CombineTutorialAgent,
  ErrorHandlerAgent
} from '../agents';

/**
 * Creates and configures the Code2Documentation PangeaFlow workflow
 * 
 * This function sets up the complete workflow for transforming code into documentation:
 * 1. Repository Fetching - Fetch and prepare code files
 * 2. Abstraction Identification - Identify key abstractions
 * 3. Relationship Analysis - Analyze relationships between abstractions
 * 4. Chapter Ordering - Determine optimal tutorial structure
 * 5. Chapter Writing - Generate individual chapters
 * 6. Tutorial Assembly - Combine into final tutorial
 */
export function createCode2DocumentationWorkflow(eventBus?: any, telemetry?: any): WorkflowOrchestrator {
  // Create event bus and telemetry if not provided
  const workflowEventBus = eventBus || new EventBus();
  const workflowTelemetry = telemetry || new TelemetryCollector();

  const builder = WorkflowBuilder.create();

  // Create agents
  const fetchRepoAgent = new FetchRepoAgent(workflowEventBus, workflowTelemetry);
  const identifyAbstractionsAgent = new IdentifyAbstractionsAgent(workflowEventBus, workflowTelemetry, 5, 20);
  const analyzeRelationshipsAgent = new AnalyzeRelationshipsAgent(workflowEventBus, workflowTelemetry, 5, 20);
  const orderChaptersAgent = new OrderChaptersAgent(workflowEventBus, workflowTelemetry, 5, 20);
  const writeChaptersAgent = new WriteChaptersAgent(workflowEventBus, workflowTelemetry, 5);
  const combineTutorialAgent = new CombineTutorialAgent(workflowEventBus, workflowTelemetry);
  const errorHandlerAgent = new ErrorHandlerAgent(workflowEventBus, workflowTelemetry);

  // Register agents directly with the orchestrator
  const orchestrator = builder['orchestrator'] as any;
  orchestrator.registerComponent(fetchRepoAgent);
  orchestrator.registerComponent(identifyAbstractionsAgent);
  orchestrator.registerComponent(analyzeRelationshipsAgent);
  orchestrator.registerComponent(orderChaptersAgent);
  orchestrator.registerComponent(writeChaptersAgent);
  orchestrator.registerComponent(combineTutorialAgent);
  orchestrator.registerComponent(errorHandlerAgent);

  // Add memory agent for state management
  builder.addMemoryAgent('memory');

  // Define workflow routes
  builder
    .route('start', 'fetch-repo')
    .route('identify-abstractions', 'identify-abstractions')
    .route('analyze-relationships', 'analyze-relationships')
    .route('order-chapters', 'order-chapters')
    .route('write-chapters', 'write-chapters')
    .route('combine-tutorial', 'combine-tutorial')
    .route('complete', 'memory')
    .route('error', 'error-handler');

  return builder.build();
}

/**
 * Execute the Code2Documentation workflow with the provided shared store
 */
export async function executeCode2DocumentationWorkflow(params: SharedStore): Promise<any> {
  const eventBus = new EventBus();
  const telemetry = new TelemetryCollector();

  const workflow = createCode2DocumentationWorkflow(eventBus, telemetry);

  // Initialize context with input parameters
  const context = {
    sharedState: {
      ...params,
      retryCount: 0
    }
  };
  console.log('Executing Code2Documentation workflow with context:', context);

  try {
    // Set up comprehensive event listeners for monitoring
    const progressListener = workflow.on('agent.status', (event) => {
      const payload = event.payload as any;
      console.log(`🤖 Agent ${payload?.agentName || 'Unknown'}: ${payload?.message || 'Status update'}`);

      // Emit events through the legacy system for UI compatibility
      import('../utils/events').then(({ emitProgress, emitGraphStatus }) => {
        const agentName = payload?.agentName || 'Unknown';
        const progress = payload?.progress || 0;
        const message = payload?.message || 'Status update';

        // Emit both progress and graph status for UI synchronization
        emitProgress(agentName, progress, message);
        emitGraphStatus(agentName, progress, message);
      }).catch(err => {
        console.error('Failed to emit progress:', err);
      });
    });

    const errorListener = workflow.on('error', (event) => {
      const payload = event.payload as any;
      console.error('💥 Workflow error detected:', payload);

      // Emit error event through legacy system for UI compatibility
      import('../utils/events').then(({ agentEvents, AgentEventType }) => {
        agentEvents.emit(AgentEventType.ERROR, {
          error: payload?.error || new Error(payload?.message || 'Unknown workflow error'),
          message: payload?.message || 'Unknown workflow error',
          component: 'Code2Documentation',
          stage: 'workflow-execution'
        });
      }).catch(err => {
        console.error('Failed to emit error:', err);
      });
    });

    const stepListener = workflow.on('step.completed', (event) => {
      console.log(`✅ Step completed:`, event.payload);
    });

    const stepErrorListener = workflow.on('step.failed', (event) => {
      console.error(`❌ Step failed:`, event.payload);

      // Emit error for failed steps through legacy system
      import('../utils/events').then(({ agentEvents, AgentEventType }) => {
        const payload = event.payload as any;
        agentEvents.emit(AgentEventType.ERROR, {
          error: payload?.error || new Error(payload?.message || 'Unknown step error'),
          message: `Step failed: ${payload?.message || 'Unknown step error'}`,
          component: 'Code2Documentation',
          stage: 'step-execution'
        });
      }).catch(err => {
        console.error('Failed to emit step error:', err);
      });
    });

    // Execute the workflow
    console.log('🔄 Executing Code2Documentation workflow...');
    const results = await workflow.execute('start', context);

    console.log('📊 Code2Documentation workflow execution completed. Raw results:', results);

    // Analyze results for errors
    if (!results) {
      const error = new Error('Workflow returned no results');
      console.error('💥 Workflow failure: No results returned');
      throw error;
    }

    if (Array.isArray(results)) {
      const failedSteps = results.filter(result => !result.success);
      if (failedSteps.length > 0) {
        console.error('💥 Workflow had failed steps:', failedSteps);
        const error = new Error(`Workflow failed at ${failedSteps.length} step(s): ${failedSteps.map(s => s.error?.message || 'Unknown error').join(', ')}`);
        throw error;
      }

      console.log('✅ All Code2Documentation workflow steps completed successfully');
    }

    // Clean up listeners
    progressListener();
    errorListener();
    stepListener();
    stepErrorListener();

    // Emit completion event through legacy system
    import('../utils/events').then(({ agentEvents, AgentEventType }) => {
      agentEvents.emit(AgentEventType.COMPLETE, {
        success: true,
        data: results,
        message: 'Code2Documentation workflow completed successfully'
      });
    }).catch(err => {
      console.error('Failed to emit completion:', err);
    });

    // Return success result
    return {
      success: true,
      output: results,
      events: [],
      nextActions: [],
      sharedStateUpdates: context.sharedState
    };

  } catch (error) {
    console.error('Code2Documentation workflow execution error:', error);

    // Emit error event through legacy system for UI compatibility
    import('../utils/events').then(({ agentEvents, AgentEventType }) => {
      agentEvents.emit(AgentEventType.ERROR, {
        error: error as Error,
        message: (error as Error).message,
        component: 'Code2Documentation',
        stage: 'workflow-execution'
      });
    }).catch(err => {
      console.error('Failed to emit workflow error:', err);
    });

    return {
      success: false,
      error: error as Error,
      events: [],
      nextActions: [],
     sharedStateUpdates: context.sharedState
    };
  }
}

/**
 * Create a default shared store with common defaults
 */
export function createDefaultSharedStore(overrides: Partial<SharedStore> = {}): SharedStore {
  return {
    user_id: 'anonymous',
    selected_files: [],
    language: 'english',
    use_cache: true,
    max_abstraction_num: 10,
    final_output_dir: './output',
    ...overrides
  };
}

/**
 * Validate shared store has required fields
 */
export function validateSharedStore(shared: SharedStore): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!shared.user_id) {
    errors.push('user_id is required');
  }

  if (!shared.repo_url && !shared.local_dir) {
    errors.push('Either repo_url or local_dir is required');
  }

  if (!shared.selected_files || shared.selected_files.length === 0) {
    errors.push('selected_files array cannot be empty');
  }

  if (!shared.language) {
    errors.push('language is required');
  }

  if (typeof shared.use_cache !== 'boolean') {
    errors.push('use_cache must be a boolean');
  }

  if (!shared.max_abstraction_num || shared.max_abstraction_num < 1) {
    errors.push('max_abstraction_num must be a positive number');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}
