# PangeaFlow Workflow Diagram Generator

A utility to generate PNG diagrams from PangeaFlow `WorkflowOrchestrator` instances. This tool creates visual flowcharts showing the **actual workflow flow** between action steps, similar to traditional flowcharts where each step leads to the next, without modifying the core PangeaFlow code.

## Features

- 🔄 **Sequential Workflow Flow**: Shows the actual flow between workflow steps (start → extract-concepts → plan-tutorial → etc.)
- 🎨 **Visual Workflow Representation**: Generate clear flowcharts with vertical top-to-bottom flow
- 🎯 **Customizable Styling**: Control colors, fonts, sizes, and layout options
- 🧠 **Smart Flow Detection**: Automatically recognizes common workflow patterns (Code2Tutor, etc.)
- 📊 **Component Details**: Show which components execute at each workflow step
- 📁 **File Output**: Save diagrams as PNG files for use in documentation or frontend

## Installation

The utility uses the `canvas` package for PNG generation. Make sure it's installed:

```bash
npm install canvas
```

## Basic Usage

```typescript
import { WorkflowBuilder } from '../pangeaflow';
import { generatePangeaFlowDiagram } from '../pangeaflow/utils/workflowDiagramGenerator';

// Create your workflow
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('processor', tools)
  .addMemoryAgent('memory')
  .route('start', 'planner')
  .route('process', 'processor')
  .route('store', 'memory')
  .build();

// Generate diagram
await generatePangeaFlowDiagram(workflow, './my-workflow.png');
```

## Advanced Usage with Custom Styling

```typescript
await generatePangeaFlowDiagram(workflow, './styled-workflow.png', {
  width: 1400,
  height: 1000,
  backgroundColor: '#f0f4f8',
  componentColor: '#e6f3ff',
  actionColor: '#fff2e6',
  edgeColor: '#2563eb',
  textColor: '#1e293b',
  fontSize: 14,
  showComponentTypes: true,
  showActionLabels: true,
});
```

## Configuration Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `width` | number | 1200 | Canvas width in pixels |
| `height` | number | 800 | Canvas height in pixels |
| `backgroundColor` | string | '#ffffff' | Background color |
| `nodeColor` | string | '#e3f2fd' | Workflow step node color |
| `edgeColor` | string | '#1976d2' | Edge/arrow color |
| `textColor` | string | '#212121' | Text color |
| `fontSize` | number | 12 | Font size for labels |
| `padding` | number | 50 | Padding around diagram |
| `showComponentDetails` | boolean | true | Show component details in nodes |

## Diagram Layout

The generator creates a **vertical workflow flow diagram**:
- **Top-to-Bottom Flow**: Each workflow step flows to the next step vertically
- **Action Nodes**: Each node represents a workflow action step (start, extract-concepts, etc.)
- **Component Details**: Shows which components execute at each step
- **Arrows**: Show the sequential flow direction between steps
- **Smart Ordering**: Automatically detects and orders workflow steps logically

## Example Workflows

### Simple Workflow
```typescript
const simpleWorkflow = WorkflowBuilder.create()
  .addReasoningAgent('analyzer', llmProvider)
  .addToolAgent('processor', tools)
  .addMemoryAgent('storage')
  .route('start', 'analyzer')
  .route('process', 'processor')
  .route('store', 'storage')
  .build();
```

### Complex Workflow with Branching
```typescript
const complexWorkflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addReasoningAgent('reviewer', llmProvider)
  .addToolAgent('fetcher', fetchTools)
  .addToolAgent('generator', genTools)
  .addMemoryAgent('memory')
  .route('start', 'planner')
  .route('fetch-data', 'fetcher')
  .route('generate', 'generator', 'reviewer')
  .route('review', 'reviewer', 'memory')
  .route('retry', 'planner')
  .build();
```

## Frontend Integration

The generated PNG files can be easily integrated into web applications:

```html
<!-- Direct image display -->
<img src="/path/to/workflow-diagram.png" alt="Workflow Diagram" />
```

```javascript
// Dynamic loading in React/Vue/etc
const workflowImage = '/api/workflow-diagram.png';
```

## API Reference

### `generatePangeaFlowDiagram(orchestrator, outputPath, options?)`

Generates a PNG diagram from a PangeaFlow workflow.

**Parameters:**
- `orchestrator: WorkflowOrchestrator` - The workflow to visualize
- `outputPath: string` - Path where the PNG should be saved
- `options?: PangeaFlowDiagramOptions` - Optional styling configuration

**Returns:** `Promise<void>`

### `PangeaFlowDiagramGenerator`

Main class for generating diagrams. Use the utility function above for most cases.

## Error Handling

```typescript
try {
  await generatePangeaFlowDiagram(workflow, './diagram.png');
  console.log('Diagram generated successfully!');
} catch (error) {
  console.error('Failed to generate diagram:', error);
}
```

## Examples

See `src/examples/pangeaFlowDiagramExample.ts` for complete examples with different styling options and workflow configurations.

## Limitations

- Currently supports two-column layout (may be enhanced in future versions)
- Requires `canvas` package for PNG generation
- File system access required for saving PNG files

## Contributing

This utility is designed to be independent of the core PangeaFlow system. Enhancements and improvements are welcome!
