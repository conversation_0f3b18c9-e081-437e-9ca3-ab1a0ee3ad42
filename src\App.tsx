import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import ProtectedRoute from "./components/ProtectedRoute";

import Landing from "./pages/Landing";
import Gallery from "./pages/Gallery";
import PublicGallery from "./pages/PublicGallery";
import TutorialDetails from "./pages/TutorialDetails";
import Settings from "./pages/Settings";
import AdminDashboard from "./pages/AdminDashboard";
import NotFound from "./pages/NotFound";
import TutorialCreationStatus from "./pages/TutorialCreationStatus";
import AuthPage from "./pages/AuthPage";
import SubscriptionPage from "./pages/SubscriptionPage";
import { LandingLayout } from "./components/layouts/LandingLayout";
import { DashboardLayout } from "./components/layouts/DashboardLayout";
import Create from "./pages/Create";
import CreateTutor from "./pages/CreateTutor";
import TutorCreationStatus from "./pages/TutorCreationStatus";
import TutorGallery from "./pages/TutorGallery";
import SharedStatePropagationTest from "./pages/SharedStatePropagationTest";
import { TutorialLayout } from "./components/layouts/TutorialLayout";
import Index from "./pages/Index";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/public" element={<Index />} />

          <Route path="/" element={<LandingLayout />}>
            <Route index element={<Landing />} />
            <Route path="/public-gallery" element={<PublicGallery />} />
            <Route path="/auth" element={<AuthPage />} />
            <Route path="*" element={<NotFound />} />
          </Route>

          <Route
            path="/admin"
            element={
              <ProtectedRoute>
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route path="/tutorial" element={<TutorialLayout />}>
            <Route path=":id" element={<TutorialDetails />} />
          </Route>

          <Route path="/dashboard" element={<DashboardLayout />}>
            <Route element={<ProtectedRoute />}>
              <Route index element={<Create />} />

              <Route path="gallery" element={<Gallery />} />
              <Route path="subscription" element={<SubscriptionPage />} />
              <Route
                path="tutorial-creation-status"
                element={<TutorialCreationStatus />}
              />

              {/* Code2Tutor Routes */}
              <Route path="create-tutor" element={<CreateTutor />} />
              <Route path="tutor-creation-status" element={<TutorCreationStatus />} />
              <Route path="tutor-gallery" element={<TutorGallery />} />

              {/* Testing Routes */}
              <Route path="test-shared-state" element={<SharedStatePropagationTest />} />

              <Route path="settings" element={<Settings />} />
            </Route>
          </Route>
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
