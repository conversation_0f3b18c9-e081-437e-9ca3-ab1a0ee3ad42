export const CONCEPT_EXTRACTION_PROMPT = `For the project \${project_name}:

Codebase Context:
\${context}

\${language_instruction}Analyze the codebase to identify key learning concepts that would be valuable for someone learning this technology/framework.

Your goal is to extract 5-\${max_concepts} core learning concepts that:
1. Are fundamental to understanding how this codebase works
2. Build upon each other in a logical learning progression
3. Are appropriate for \${target_audience} level learners
4. Can be taught through practical examples from this code

For each learning concept, provide:
1. A clear \`name\` that describes the concept\${name_lang_hint}
2. A \`description\` explaining what this concept teaches (100-150 words)\${desc_lang_hint}
3. The \`difficulty\` level: beginner, intermediate, or advanced
4. A list of \`prerequisites\` (other concept names that should be learned first)
5. Relevant \`file_indices\` (integers) that demonstrate this concept
6. Practical \`examples\` of how this concept is used in the codebase

List of file indices and paths present in the context:
\${file_listing_for_prompt}

Focus on concepts that:
- Have clear, demonstrable examples in the code
- Build practical skills
- Are essential for understanding the project's architecture
- Can be learned through hands-on exercises

Format the output as a YAML list of dictionaries:

\`\`\`yaml
concepts:
  - name: "Concept Name"
    description: "What this concept teaches and why it's important for learners..."
    difficulty: "beginner|intermediate|advanced"
    prerequisites: ["prerequisite concept names"]
    file_indices: [1, 3, 5]
    examples: ["Specific example of how this concept is used", "Another example"]
\`\`\`

Ensure concepts are ordered from foundational to advanced, and that prerequisites reference earlier concepts in the list.`;
