import { Flow, Node, NodeMonitor, FlowMonitor } from '../pocketflow';

// Define a simple flow
class InputNode extends Node {
  async exec(): Promise<string> {
    return "Processing input";
  }
  
  async post(shared: any): Promise<string> {
    return "process";
  }
}

class ProcessNode extends Node {
  async exec(): Promise<string> {
    // Simulate work
    await new Promise(resolve => setTimeout(resolve, 500));
    return "Processing data";
  }
  
  async post(shared: any): Promise<string> {
    return Math.random() > 0.5 ? "success" : "error";
  }
}

class SuccessNode extends Node {
  async exec(): Promise<string> {
    return "Success!";
  }
}

class ErrorNode extends Node {
  async exec(): Promise<string> {
    throw new Error("Something went wrong");
  }
  
  async execFallback(prepRes: unknown, error: Error): Promise<string> {
    return "Recovered from error";
  }
}

// Create the flow
const input = new InputNode();
const process = new ProcessNode();
const success = new SuccessNode();
const error = new ErrorNode(3, 1); // 3 retries, 1 second wait

input.on("process", process);
process.on("success", success);
process.on("error", error);

const flow = new Flow(input);

// Monitor a single node
async function monitorNode() {
  console.log("=== Monitoring single node ===");
  const monitor = new NodeMonitor(process);
  await monitor.run({});
  console.log("Node metrics:", monitor.getMetrics());
  console.log("Total duration:", monitor.getTotalDuration(), "ms");
}

// Monitor the entire flow
async function monitorFlow() {
  console.log("\n=== Monitoring flow ===");
  const monitor = new FlowMonitor(flow);
  const shared = {};
  await monitor.run(shared);
  monitor.printSummary();
}

// Run the examples
async function main() {
  await monitorNode();
  await monitorFlow();
}

main().catch(console.error);