// Create a simple browser-compatible EventEmitter
type Listener = (...args: unknown[]) => void;

class BrowserEventEmitter {
  private listeners: Record<string, Listener[]> = {};

  on(event: string, listener: Listener): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(listener);
  }

  off(event: string, listener: Listener): void {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(l => l !== listener);
  }

  emit(event: string, ...args: unknown[]): void {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(listener => listener(...args));
  }
}

// Create a dedicated event emitter for the Code2Documentation agent
export const agentEvents = new BrowserEventEmitter();

// Event types
export enum AgentEventType {
  PROGRESS = 'progress',
  ERROR = 'error',
  COMPLETE = 'complete',
  GRAPH_STATUS = 'graph_status'
}

// Progress event data interface
export interface ProgressEvent {
  stage: string;
  progress: number;
  message?: string;
}

export const emitGraphStatus = (stage: string, progress: number, message?: string): void => {
  agentEvents.emit(AgentEventType.GRAPH_STATUS, { stage, progress, message });
};

// Helper functions to emit events
export const emitProgress = (stage: string, progress: number, message?: string): void => {
  agentEvents.emit(AgentEventType.PROGRESS, { stage, progress, message });
};

export const emitError = (error: Error): void => {
  agentEvents.emit(AgentEventType.ERROR, error);
};

export const emitComplete = (result: unknown): void => {
  // First emit a final progress update to ensure 100% completion
  agentEvents.emit(AgentEventType.PROGRESS, {
    stage: "Tutorial Generation",
    progress: 100,
    message: "Tutorial generation completed successfully"
  });

  // Then emit the complete event with the result
  agentEvents.emit(AgentEventType.COMPLETE, result);
};
