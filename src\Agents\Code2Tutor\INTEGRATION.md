# Code2Tutor Integration Guide

This document explains how the new Code2Tutor agent has been integrated into the existing CodeTutor Pro application.

## Overview

Code2Tutor is a new agent that transforms code repositories into interactive educational tutorials, complementing the existing Code2Documentation functionality. It uses the modern PangeaFlow architecture for reactive workflow orchestration.

## New Pages Created

### 1. CreateTutor (`/dashboard/create-tutor`)
- **Purpose**: Configuration page for creating interactive tutorials
- **Features**: 
  - Tutorial settings (audience, format, language)
  - Interactive features toggles (exercises, diagrams, examples)
  - Repository crawler integration
  - Real-time validation and usage limits

### 2. TutorCreationStatus (`/dashboard/tutor-creation-status`)
- **Purpose**: Real-time status monitoring during tutorial creation
- **Features**:
  - Live progress tracking with agent status
  - Detailed activity logs
  - Stage-by-stage progress visualization
  - Event-driven updates from PangeaFlow

### 3. TutorGallery (`/dashboard/tutor-gallery`)
- **Purpose**: Browse and manage created interactive tutorials
- **Features**:
  - Search and filter capabilities
  - Tutorial metadata display
  - Learning objectives and prerequisites
  - Direct links to tutorials

## Navigation Updates

### Enhanced NavBar
The navigation has been updated to distinguish between the two content types:

- **Create Dropdown**:
  - Documentation (existing functionality)
  - Interactive Tutorial (new Code2Tutor)

- **Browse Dropdown**:
  - Documentation Gallery (existing)
  - Tutorial Gallery (new)

## Architecture Differences

| Aspect | Code2Documentation | Code2Tutor |
|--------|-------------------|-------------|
| **Architecture** | PocketFlow (linear) | PangeaFlow (reactive) |
| **Purpose** | Technical documentation | Educational tutorials |
| **Content Focus** | API reference, structure | Learning concepts, exercises |
| **Target Audience** | Developers | Learners at all levels |
| **Interactivity** | Static documentation | Exercises, quizzes, challenges |

## Database Schema

Code2Tutor reuses the existing `tutorial_metadata` table with a new `tutorial_type` field:

```sql
-- Existing tutorials have tutorial_type = 'documentation'
-- New tutorials have tutorial_type = 'interactive_tutorial'
```

Additional fields used by Code2Tutor:
- `target_audience`: 'beginner' | 'intermediate' | 'advanced'
- `estimated_time`: Number (minutes)
- `learning_objectives`: JSON array
- `prerequisites`: JSON array

## Event System

Code2Tutor uses a dedicated event system for real-time updates:

```typescript
// Event types
TutorEventType.PROGRESS        // Overall progress updates
TutorEventType.AGENT_STATUS    // Individual agent status
TutorEventType.CONCEPT_EXTRACTED // When concepts are identified
TutorEventType.SECTION_GENERATED // When sections are created
TutorEventType.COMPLETE        // Tutorial completion
TutorEventType.ERROR           // Error handling
```

## Usage Example

```typescript
import { executeCode2TutorFlow, createDefaultSharedStore } from '@/Agents/Code2Tutor';

const shared = createDefaultSharedStore({
  user_id: 'user123',
  repo_url: 'https://github.com/example/repo',
  project_name: 'React Tutorial',
  target_audience: 'beginner',
  include_exercises: true
});

const results = await executeCode2TutorFlow(shared);
```

## Integration Points

### 1. Existing Components Reused
- `GitHubRepoCrawler`: File selection and repository analysis
- `TrialStatusBanner`: Usage limits and subscription status
- `SubscriptionPlans`: Payment and plan management
- Authentication and user management

### 2. New Components Created
- `CreateOptionsCard`: Choice between documentation and tutorial
- Tutorial-specific UI components
- PangeaFlow workflow orchestration

### 3. Shared Infrastructure
- Supabase storage and database
- LLM integration (OpenRouter)
- User authentication and permissions
- Subscription and usage tracking

## Testing

To test the Code2Tutor functionality:

1. Navigate to `/dashboard/create-tutor`
2. Enter a GitHub repository URL
3. Select files and configure tutorial settings
4. Monitor progress on the status page
5. View completed tutorial in the gallery

## Future Enhancements

- **Adaptive Learning**: Adjust difficulty based on user progress
- **Code Execution**: Run code examples in sandboxed environments
- **Collaborative Features**: Share and collaborate on tutorials
- **Analytics**: Track learning effectiveness
- **Multi-modal**: Video and audio content support

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all Code2Tutor dependencies are installed
2. **Event Handling**: Check browser console for event listener errors
3. **Database Issues**: Verify tutorial_metadata table has required fields
4. **LLM Errors**: Check OpenRouter API key and rate limits

### Debug Mode

Enable debug logging by setting:
```typescript
localStorage.setItem('code2tutor-debug', 'true');
```

This will provide detailed console output for troubleshooting workflow issues.

## Deployment Notes

When deploying Code2Tutor:

1. Ensure all new routes are properly configured
2. Update database migrations if needed
3. Test PangeaFlow workflow execution
4. Verify event system functionality
5. Check LLM integration and rate limits

The Code2Tutor agent is designed to coexist seamlessly with the existing Code2Documentation functionality while providing a modern, reactive architecture for future enhancements.
