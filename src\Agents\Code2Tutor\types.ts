// src/Agents/Code2Tutor/types.ts

/**
 * Shared store used across the PangeaFlow Code2Tutor agent
 */

export interface LearningConcept {
  name: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  files: number[];
  examples: string[];
}

export interface TutorialSection {
  id: string;
  title: string;
  concept: string;
  content: string;
  exercises: Exercise[];
  codeExamples: CodeExample[];
}

export interface Exercise {
  id: string;
  type: 'coding' | 'quiz' | 'explanation' | 'debugging';
  title: string;
  description: string;
  solution?: string;
  hints: string[];
  difficulty: 'easy' | 'medium' | 'hard';
}

export interface CodeExample {
  id: string;
  title: string;
  code: string;
  language: string;
  explanation: string;
  runnable: boolean;
  expectedOutput?: string;
}

export interface TutorialMetadata {
  title: string;
  description: string;
  targetAudience: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: number; // in minutes
  prerequisites: string[];
  learningObjectives: string[];
}

export interface ConceptRelationship {
  from: string;
  to: string;
  type: 'prerequisite' | 'builds-on' | 'related' | 'example-of';
  strength: number; // 0-1
}

export interface TutorialStructure {
  metadata: TutorialMetadata;
  concepts: LearningConcept[];
  relationships: ConceptRelationship[];
  sections: TutorialSection[];
  progressionPath: string[]; // ordered concept IDs
}

// Main shared store for the Code2Tutor agent
export interface SharedStore {
  // User inputs
  user_id: string;
  session_id?: string;
  tutorial_id?: string;

  // Repository information
  repo_url?: string;
  local_dir?: string;
  project_name?: string;
  github_token?: string;

  // Tutorial configuration
  target_audience: 'beginner' | 'intermediate' | 'advanced';
  content_language: string;
  tutorial_format: 'interactive' | 'guided' | 'self-paced';
  include_exercises: boolean;
  include_diagrams: boolean;
  include_examples: boolean;
  max_concepts: number;

  // File processing
  selected_files: string[];
  language: string;
  use_cache: boolean;

  // Intermediate results (filled by agents)
  files?: [string, string][]; // Array of [path, content]
  concepts?: LearningConcept[]; // Extracted learning concepts
  tutorial_structure?: TutorialStructure; // Planned tutorial structure
  sections?: TutorialSection[]; // Generated tutorial sections
  final_tutorial?: string; // Assembled tutorial content

  // Output
  output_dir?: string;
  final_output_dir?: string;
}

// Event types for the Code2Tutor agent
export interface TutorProgressEvent {
  stage: string;
  progress: number;
  message?: string;
  conceptsFound?: number;
  sectionsGenerated?: number;
}

export interface TutorCompleteEvent {
  success: boolean;
  message: string;
  tutorialId?: string;
  tutorialUrl?: string;
}

// Agent-specific context for PangeaFlow
export interface TutorExecutionContext {
  shared: SharedStore;
  currentConcept?: string;
  currentSection?: string;
  retryCount?: number;
  metadata?: Record<string, unknown>;
}
