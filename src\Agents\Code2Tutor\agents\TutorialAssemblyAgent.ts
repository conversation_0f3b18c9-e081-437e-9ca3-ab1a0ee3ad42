// src/Agents/Code2Tutor/agents/TutorialAssemblyAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow/pangeaflow';
import { SharedStore } from '../types';
import { emitAgentStatus, emitTutorProgress, emitTutorComplete } from '../utils/events';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';
import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';
import { TUTORIAL_ASSEMBLY_PROMPT } from '../prompts';
import { supabase } from '@/integrations/supabase/client';
// import { toast } from '@/hooks/use-toast'; // Removed as not currently used

/**
 * TutorialAssemblyAgent - Assembles the final tutorial and saves it
 * 
 * This agent is responsible for:
 * - Combining all generated sections into a cohesive tutorial
 * - Creating table of contents and navigation
 * - Adding introduction and conclusion sections
 * - Saving the tutorial to storage (Supabase)
 * - Generating tutorial metadata for the database
 */
export class TutorialAssemblyAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 3) {
    super('tutorial-assembly', eventBus, telemetry, {
      stage: 'tutorial-assembly',
      progress: 0
    });
    this.maxRetries = maxRetries;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('tutorial-assembly', async () => {
      emitAgentStatus('TutorialAssemblyAgent', 'starting', 0, 'Initializing tutorial assembly');

      const shared = context.sharedState.shared as SharedStore;

      try {
        // Validate critical inputs first
        if (!shared.tutorial_id) {
          throw new Error('Tutorial ID is missing from shared store - cannot proceed with assembly');
        }

        if (!shared.user_id) {
          throw new Error('User ID is missing from shared store - cannot proceed with assembly');
        }

        if (!shared.sections || shared.sections.length === 0) {
          throw new Error('No tutorial sections available for assembly');
        }

        if (!shared.tutorial_structure) {
          throw new Error('No tutorial structure available for assembly');
        }

        console.log('🔄 TutorialAssemblyAgent: Starting assembly with:', {
          tutorial_id: shared.tutorial_id,
          user_id: shared.user_id,
          sections_count: shared.sections.length,
          project_name: shared.project_name
        });

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 10, 'Preparing tutorial components');
        emitTutorProgress('Tutorial Assembly', 10, 'Assembling tutorial components');

        // Prepare sections content for assembly
        const sectionsContent = this.prepareSectionsContent(shared.sections);
        const tutorialMetadata = shared.tutorial_structure.metadata;

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 30, 'Generating final tutorial structure');

        // Build prompt for tutorial assembly
        const prompt = buildPrompt(TUTORIAL_ASSEMBLY_PROMPT, {
          project_name: shared.project_name || 'Unknown Project',
          tutorial_metadata: this.formatTutorialMetadata(tutorialMetadata),
          sections_content: sectionsContent,
          language_instruction: this.getLanguageInstruction(shared.content_language)
        });

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 50, 'Assembling complete tutorial with LLM');

        // Call LLM for tutorial assembly
        const assembledTutorial = await callLlm_openrouter({
          tutorial_id: shared.tutorial_id,
          prompt,
          temperature: 0.3, // Lower temperature for consistent assembly
          model: "google/gemini-2.5-flash-preview-05-20",
          use_cache: shared.use_cache && this.currentRetry === 0,
          user_id: shared.user_id
        });

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 70, 'Saving tutorial to storage');

        // Save tutorial to storage
        const tutorialId = await this.saveTutorialToStorage(
          assembledTutorial,
          shared,
          tutorialMetadata
        );

        emitAgentStatus('TutorialAssemblyAgent', 'processing', 90, 'Generating tutorial metadata');

        // Update shared store
        shared.final_tutorial = assembledTutorial;
        shared.tutorial_id = tutorialId;

        emitAgentStatus('TutorialAssemblyAgent', 'completed', 100, 'Tutorial assembly completed successfully');
        emitTutorProgress('Tutorial Assembly', 100, 'Tutorial successfully created and saved');

        // Get tutorial URL
        const tutorialUrl = this.getTutorialUrl(tutorialId);

        // Emit completion event
        emitTutorComplete(true, 'Tutorial successfully generated', tutorialId, tutorialUrl);

        this.emit('tutorial.assembled', {
          tutorialId,
          tutorialUrl,
          sectionCount: shared.sections.length
        }, context.id);

        return {
          success: true,
          output: {
            tutorialId,
            tutorialUrl,
            tutorial: assembledTutorial
          },
          events: [],
          nextActions: ['complete'],
          sharedStateUpdates: {
            // ✅ Preserve existing shared state and add new data
            ...context.sharedState,
            tutorialId,
            sectionsAssembled: shared.sections.length,
            estimatedTime: tutorialMetadata.estimatedTime,
            completed_at: new Date().toISOString()
          }
        };

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('❌ TutorialAssemblyAgent: Error during execution:', error);

        this.currentRetry++;

        // Don't retry for validation errors or critical database errors
        const isRetryableError = !errorMessage.includes('missing from shared store') &&
                                !errorMessage.includes('Failed to save tutorial metadata') &&
                                !errorMessage.includes('Tutorial ID is missing');

        if (this.currentRetry < this.maxRetries && isRetryableError) {
          console.log(`🔄 TutorialAssemblyAgent: Retrying ${this.currentRetry}/${this.maxRetries}`);
          emitAgentStatus('TutorialAssemblyAgent', 'processing', 0, `Retry ${this.currentRetry}/${this.maxRetries}: ${errorMessage}`);

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000 * this.currentRetry));

          // Retry execution
          return this.execute(context);
        }

        // Final failure - emit comprehensive error information
        const finalErrorMessage = `Tutorial assembly failed: ${errorMessage}`;
        console.error('💥 TutorialAssemblyAgent: Final failure after', this.currentRetry, 'attempts');

        emitAgentStatus('TutorialAssemblyAgent', 'error', 0, finalErrorMessage);
        emitTutorComplete(false, finalErrorMessage);

        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          sharedStateUpdates: {
            stage: 'tutorial-assembly',
            retries: this.currentRetry,
            error_message: finalErrorMessage,
            failed_at: new Date().toISOString()
          }
        };
      }
    });
  }

  /**
   * Prepare sections content for assembly
   */
  private prepareSectionsContent(sections: any[]): string {
    return sections.map((section, index) => {
      let sectionContent = `\n## Section ${index + 1}: ${section.title}\n\n`;
      sectionContent += section.content || 'Content not available';
      
      // Add exercises if present
      if (section.exercises && section.exercises.length > 0) {
        sectionContent += '\n\n### Exercises\n\n';
        section.exercises.forEach((exercise: any, exerciseIndex: number) => {
          sectionContent += `**Exercise ${exerciseIndex + 1}: ${exercise.title}**\n\n`;
          sectionContent += `${exercise.description}\n\n`;
        });
      }

      // Add code examples if present
      if (section.codeExamples && section.codeExamples.length > 0) {
        sectionContent += '\n\n### Code Examples\n\n';
        section.codeExamples.forEach((example: any, exampleIndex: number) => {
          sectionContent += `**Example ${exampleIndex + 1}: ${example.title}**\n\n`;
          sectionContent += `\`\`\`${example.language}\n${example.code}\n\`\`\`\n\n`;
          sectionContent += `${example.explanation}\n\n`;
        });
      }

      return sectionContent;
    }).join('\n---\n');
  }

  /**
   * Format tutorial metadata for the prompt
   */
  private formatTutorialMetadata(metadata: any): string {
    return `
Title: ${metadata.title}
Description: ${metadata.description}
Target Audience: ${metadata.targetAudience}
Estimated Time: ${metadata.estimatedTime} minutes
Learning Objectives:
${metadata.learningObjectives.map((obj: string) => `- ${obj}`).join('\n')}
Prerequisites:
${metadata.prerequisites.map((prereq: string) => `- ${prereq}`).join('\n')}
    `.trim();
  }

  /**
   * Save tutorial to Supabase storage and database
   */
  private async saveTutorialToStorage(
    tutorialContent: string,
    shared: SharedStore,
    metadata: any
  ): Promise<string> {
    // Validate tutorial_id exists
    if (!shared.tutorial_id) {
      throw new Error('Tutorial ID is missing from shared store');
    }

    const tutorialId = shared.tutorial_id;
    const fileName = `${tutorialId}/tutorial.md`;

    console.log('🔄 TutorialAssemblyAgent: Starting save operation with tutorial_id:', tutorialId);

    try {
      // Save tutorial content to storage
      emitAgentStatus('TutorialAssemblyAgent', 'processing', 70, 'Uploading tutorial content to storage');

      const { error: uploadError } = await supabase.storage
        .from('tutorials')
        .upload(fileName, tutorialContent, {
          contentType: 'text/markdown',
          upsert: true
        });

      if (uploadError) {
        console.error('❌ Storage upload failed:', uploadError);
        throw new Error(`Failed to upload tutorial: ${uploadError.message}`);
      }

      console.log('✅ Tutorial content uploaded successfully to:', fileName);

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('tutorials')
        .getPublicUrl(fileName);

      emitAgentStatus('TutorialAssemblyAgent', 'processing', 80, 'Saving tutorial metadata to database');

      // Prepare database insert data with validation
      const insertData = {
        tutorial_id: tutorialId,
        project_name: shared.project_name || 'Unknown Project',
        index_url: urlData.publicUrl,
        chapter_urls: [urlData.publicUrl], // Single file tutorial
        description: metadata.description || 'No description available',
        repo_url: shared.repo_url || null,
        language: shared.content_language || 'english',
        user_id: shared.user_id || null,
        is_public: false, // Default to private for Code2Tutor tutorials
        difficulty: metadata.targetAudience || 'beginner', // Map target_audience to difficulty
        tags: Array.isArray(metadata.learningObjectives) ? metadata.learningObjectives : [] // Store learning objectives as tags
      };

      console.log('🔄 Inserting tutorial metadata:', insertData);

      // Save tutorial metadata to database
      const { data: insertResult, error: metaError } = await supabase
        .from('tutorial_metadata')
        .insert(insertData)
        .select();

      if (metaError) {
        console.error('❌ Database insert failed:', metaError);
        console.error('Insert data that failed:', insertData);
        emitAgentStatus('TutorialAssemblyAgent', 'error', 85, `Database error: ${metaError.message}`);

        // This is a critical error - throw it to fail the workflow
        throw new Error(`Failed to save tutorial metadata: ${metaError.message}`);
      }

      if (!insertResult || insertResult.length === 0) {
        console.error('❌ Database insert returned no data');
        throw new Error('Database insert succeeded but returned no data');
      }

      console.log('✅ Tutorial metadata saved successfully:', insertResult[0]);
      emitAgentStatus('TutorialAssemblyAgent', 'processing', 90, `Tutorial saved with ID: ${tutorialId}`);

      return tutorialId;

    } catch (error) {
      console.error('Error saving tutorial:', error);
      emitAgentStatus('TutorialAssemblyAgent', 'error', 0, `Save failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get tutorial URL for sharing
   */
  private getTutorialUrl(tutorialId: string): string {
    // Construct URL based on your application's routing
    const baseUrl = window.location.origin;
    return `${baseUrl}/tutorial/${tutorialId}`;
  }

  /**
   * Get language instruction based on content language
   */
  private getLanguageInstruction(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return `Write all content in ${language}. `;
    }
    return '';
  }
}
