// src/Agents/Code2Documentation/pangeaflow/utils/events.ts

// Browser-compatible event emitter implementation
class BrowserEventEmitter {
  private listeners: Map<string, Function[]> = new Map();

  emit(event: string, data: any): void {
    const eventListeners = this.listeners.get(event) || [];
    eventListeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }

  on(event: string, listener: Function): void {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(listener);
  }

  off(event: string, listener: Function): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(listener);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }
}

// Create a global event emitter
export const eventEmitter = new BrowserEventEmitter();

// Event types
export enum EventType {
  PROGRESS = 'progress',
  STATUS = 'status',
  ERROR = 'error',
  COMPLETE = 'complete'
}

// Event interfaces
export interface ProgressEvent {
  component: string;
  progress: number;
  message: string;
}

export interface StatusEvent {
  component: string;
  status: string;
  message: string;
}

export interface ErrorEvent {
  component: string;
  message: string;
  error: Error;
}

export interface CompleteEvent {
  component: string;
  result: any;
}

// Event emission helpers
export function emitProgress(component: string, progress: number, message: string): void {
  eventEmitter.emit(EventType.PROGRESS, { component, progress, message });
}

export function emitGraphStatus(component: string, progress: number, message: string): void {
  emitProgress(component, progress, message);
  eventEmitter.emit(EventType.STATUS, { component, status: 'running', message });
}

export function emitError(component: string, message: string, error?: Error): void {
  eventEmitter.emit(EventType.ERROR, { 
    component, 
    message, 
    error: error || new Error(message) 
  });
}

export function emitComplete(component: string, result: any): void {
  eventEmitter.emit(EventType.COMPLETE, { component, result });
}

// Event subscription helpers
export function onProgress(callback: (event: ProgressEvent) => void): void {
  eventEmitter.on(EventType.PROGRESS, callback);
}

export function onStatus(callback: (event: StatusEvent) => void): void {
  eventEmitter.on(EventType.STATUS, callback);
}

export function onError(callback: (event: ErrorEvent) => void): void {
  eventEmitter.on(EventType.ERROR, callback);
}

export function onComplete(callback: (event: CompleteEvent) => void): void {
  eventEmitter.on(EventType.COMPLETE, callback);
}

// Cleanup helper
export function removeAllListeners(): void {
  eventEmitter.removeAllListeners();
}
