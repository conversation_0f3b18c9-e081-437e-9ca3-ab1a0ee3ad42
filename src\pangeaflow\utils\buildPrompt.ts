/**
 * A flexible prompt template builder that dynamically handles variable injection
 * @param template Base prompt template string with placeholders (e.g., "${variableName}" or "{{variableName}}")
 * @param variables Object containing variable names and values to inject
 * @param options Configuration options
 * @returns Processed prompt string with variables injected
 */
export function buildPrompt(
  template: string,
  variables: Record<string, any> = {},
  options: {
    defaultValue?: string;
    throwOnMissing?: boolean;
  } = {}
): string {
  const { defaultValue = "", throwOnMissing = false } = options;

  try {
    // First replace ${variableName} format
    let result = template.replace(/\${([\w\.\[\]]+)}/g, (match, path) => {
      return replaceVariable(path, variables, defaultValue, throwOnMissing);
    });
    
    // Then replace {{variableName}} format
    result = result.replace(/\{\{([\w\.\[\]]+)\}\}/g, (match, path) => {
      return replaceVariable(path, variables, defaultValue, throwOnMissing);
    });
    
    return result;
  } catch (err) {
    if (throwOnMissing) {
      throw err;
    }
    return template;
  }
}

// Helper function to handle variable replacement
function replaceVariable(
  path: string, 
  variables: Record<string, any>,
  defaultValue: string,
  throwOnMissing: boolean
): string {
  try {
    // Handle nested paths (e.g., "user.name" or "items[0].title")
    const value = path
      .split(/\.|\[|\]/)
      .filter(Boolean)
      .reduce((obj, key) => {
        // Remove closing bracket if present
        const cleanKey = key.replace("]", "");
        return obj && obj[cleanKey] !== undefined
          ? obj[cleanKey]
          : undefined;
      }, variables);

    if (value === undefined || value === null) {
      if (throwOnMissing) {
        throw new Error(`Missing variable: ${path}`);
      }
      return defaultValue;
    }

    // Handle different types of values
    if (typeof value === "object") {
      return JSON.stringify(value);
    }
    return String(value);
  } catch (err) {
    if (throwOnMissing) {
      throw err;
    }
    return defaultValue;
  }
}
