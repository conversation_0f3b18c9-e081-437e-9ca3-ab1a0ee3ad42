# Project Brief: CodeTutorPro

## Project Overview
CodeTutorPro is a SaaS application that transforms GitHub repositories into comprehensive, beginner-friendly tutorials using AI. The platform leverages the PocketFlow framework for LLM workflow orchestration and provides a complete tutorial generation pipeline.

## Core Value Proposition
- **Automated Tutorial Generation**: Convert any GitHub repository into structured, educational content
- **AI-Powered Analysis**: Intelligent code analysis and documentation generation
- **Multiple Output Formats**: Support for Markdown, HTML, and PDF tutorial formats
- **Subscription-Based Service**: Tiered pricing with trial periods and usage limits

## Key Features
1. **Repository Analysis**: Crawl and analyze GitHub repositories (public and private)
2. **Intelligent File Selection**: Smart filtering and file selection based on patterns
3. **Tutorial Generation**: AI-powered creation of step-by-step tutorials
4. **User Management**: Authentication, subscription management, and usage tracking
5. **Gallery System**: Public and private tutorial galleries
6. **Admin Dashboard**: Administrative controls and analytics

## Target Users
- **Primary**: Developers and educators who want to create tutorials from existing codebases
- **Secondary**: Development teams documenting their projects
- **Tertiary**: Educational institutions teaching programming concepts

## Technical Architecture
- **Frontend**: React + TypeScript + Vite
- **UI Framework**: shadcn/ui + Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **AI Framework**: PocketFlow for LLM workflow orchestration
- **Authentication**: Clerk for user management
- **Deployment**: Lovable platform

## Business Model
- **Trial**: 7-day full-featured trial, no credit card required
- **Professional**: $29/month - 50MB repos, 20 tutorials/month
- **Enterprise**: $99/month - Unlimited repos and tutorials
- **Enterprise Plus**: Custom pricing with advanced features

## Current Status
The application is in active development with core functionality implemented:
- Repository crawling and analysis
- Basic tutorial generation pipeline
- User authentication and subscription management
- Trial system and usage tracking
- Admin dashboard for monitoring

## Key Constraints
- Repository size limits based on subscription tier
- Monthly tutorial generation limits
- GitHub API rate limiting considerations
- LLM token usage optimization

## Success Metrics
- User conversion from trial to paid subscriptions
- Tutorial generation success rate
- User engagement and retention
- Repository processing efficiency
- Customer satisfaction with generated tutorials
